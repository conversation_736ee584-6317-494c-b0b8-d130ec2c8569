<template>
  <div class="player-highlight-card">
    <el-card class="highlight-card">
      <!-- 球员信息头部 -->
      <template #header>
        <div class="player-header">
          <div class="player-avatar">
            <img v-if="playerHighlight.playerAvatar" :src="playerHighlight.playerAvatar" :alt="playerHighlight.playerName" />
            <el-icon v-else><User /></el-icon>
          </div>
          <div class="player-info">
            <h3 class="player-name">{{ playerHighlight.playerName }}</h3>
            <div class="player-meta">
              <el-tag v-if="playerHighlight.playerNumber" type="primary">#{{ playerHighlight.playerNumber }}</el-tag>
              <el-tag v-if="playerHighlight.playerTeam">{{ playerHighlight.playerTeam }}</el-tag>
              <el-tag v-if="playerHighlight.playerPosition">{{ playerHighlight.playerPosition }}</el-tag>
            </div>
          </div>
          <div class="highlight-actions">
            <el-button type="primary" @click="playHighlight">
              <el-icon><VideoPlay /></el-icon>
              播放集锦
            </el-button>
            <el-button @click="downloadHighlight">
              <el-icon><Download /></el-icon>
              下载
            </el-button>
          </div>
        </div>
      </template>

      <!-- 集锦预览 -->
      <div class="highlight-preview">
        <div class="video-thumbnail" @click="playHighlight">
          <img v-if="playerHighlight.thumbnailUrl" :src="playerHighlight.thumbnailUrl" alt="集锦缩略图" />
          <div v-else class="thumbnail-placeholder">
            <el-icon><VideoPlay /></el-icon>
            <span>点击播放集锦</span>
          </div>
          <div class="video-duration">{{ formatDuration(playerHighlight.duration) }}</div>
          <div class="play-overlay">
            <el-icon class="play-icon"><VideoPlay /></el-icon>
          </div>
        </div>
      </div>

      <!-- 统计数据 -->
      <div class="stats-section">
        <h4 class="stats-title">个人数据统计</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">{{ playerHighlight.stats.totalHighlights }}</div>
            <div class="stat-label">精彩片段</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ playerHighlight.stats.shotsMade }}/{{ playerHighlight.stats.shotsAttempted }}</div>
            <div class="stat-label">投篮命中</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ shootingPercentage }}%</div>
            <div class="stat-label">命中率</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ playerHighlight.stats.assists }}</div>
            <div class="stat-label">助攻</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ playerHighlight.stats.steals }}</div>
            <div class="stat-label">抢断</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ playerHighlight.stats.blocks }}</div>
            <div class="stat-label">盖帽</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ formatTime(playerHighlight.stats.screenTime) }}</div>
            <div class="stat-label">出镜时间</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ playerHighlight.stats.averageConfidence.toFixed(1) }}%</div>
            <div class="stat-label">平均置信度</div>
          </div>
        </div>
      </div>

      <!-- 精彩片段列表 -->
      <div class="segments-section">
        <h4 class="segments-title">精彩片段详情</h4>
        <div class="segments-list">
          <div 
            v-for="(segment, index) in playerHighlight.segments" 
            :key="index"
            class="segment-item"
            @click="playSegment(segment)"
          >
            <div class="segment-icon">
              <component :is="getActionIcon(segment.type)" />
            </div>
            <div class="segment-info">
              <div class="segment-title">{{ segment.description }}</div>
              <div class="segment-meta">
                <span class="segment-time">{{ formatTime(segment.startTime) }} - {{ formatTime(segment.endTime) }}</span>
                <span class="segment-confidence">置信度: {{ segment.confidence.toFixed(1) }}%</span>
              </div>
            </div>
            <div class="segment-duration">
              {{ formatDuration(segment.endTime - segment.startTime) }}
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 视频播放器对话框 -->
    <el-dialog 
      v-model="playerVisible" 
      :title="`${playerHighlight.playerName} - 个人集锦`"
      width="80%"
      :before-close="closePlayer"
    >
      <div class="video-player-container">
        <video 
          ref="videoPlayer"
          controls
          width="100%"
          height="400"
          :src="playerHighlight.videoUrl"
          @loadedmetadata="onVideoLoaded"
        >
          您的浏览器不支持视频播放
        </video>
        
        <div class="player-details">
          <div class="video-info">
            <h3>{{ playerHighlight.playerName }} 个人集锦</h3>
            <p>总时长: {{ formatDuration(playerHighlight.duration) }}</p>
            <p>包含 {{ playerHighlight.stats.totalHighlights }} 个精彩片段</p>
          </div>
          
          <div class="video-actions">
            <el-button @click="downloadHighlight">
              <el-icon><Download /></el-icon>
              下载集锦
            </el-button>
            <el-button @click="shareHighlight">
              <el-icon><Share /></el-icon>
              分享
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts" name="PlayerHighlightCard">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  User, VideoPlay, Download, Share, Trophy, 
  Basketball, Aim, Shield 
} from '@element-plus/icons-vue'

// 定义 props
interface PlayerHighlight {
  playerId: string
  playerName: string
  playerNumber?: string
  playerTeam?: string
  playerPosition?: string
  playerAvatar?: string
  segments: Array<{
    startTime: number
    endTime: number
    type: string
    description: string
    confidence: number
  }>
  stats: {
    totalHighlights: number
    shotsMade: number
    shotsAttempted: number
    assists: number
    steals: number
    blocks: number
    defensiveActions: number
    screenTime: number
    averageConfidence: number
  }
  duration: number
  videoUrl?: string
  thumbnailUrl?: string
}

const props = defineProps<{
  playerHighlight: PlayerHighlight
}>()

// 响应式数据
const playerVisible = ref(false)
const videoPlayer = ref<HTMLVideoElement>()

// 计算属性
const shootingPercentage = computed(() => {
  const { shotsMade, shotsAttempted } = props.playerHighlight.stats
  return shotsAttempted > 0 ? Math.round((shotsMade / shotsAttempted) * 100) : 0
})

// 方法
const playHighlight = () => {
  if (props.playerHighlight.videoUrl) {
    playerVisible.value = true
  } else {
    ElMessage.warning('集锦视频正在生成中，请稍后再试')
  }
}

const playSegment = (segment: any) => {
  ElMessage.info(`播放片段: ${segment.description}`)
  // 这里可以跳转到特定时间点播放
}

const downloadHighlight = () => {
  if (props.playerHighlight.videoUrl) {
    const link = document.createElement('a')
    link.href = props.playerHighlight.videoUrl
    link.download = `${props.playerHighlight.playerName}_集锦.webm`
    link.click()
    ElMessage.success('开始下载集锦视频')
  } else {
    ElMessage.warning('集锦视频尚未生成')
  }
}

const shareHighlight = () => {
  const text = `${props.playerHighlight.playerName}的精彩集锦 - ${props.playerHighlight.stats.totalHighlights}个精彩片段`
  if (navigator.share) {
    navigator.share({
      title: '篮球集锦分享',
      text,
      url: window.location.href
    })
  } else {
    navigator.clipboard.writeText(text + ' ' + window.location.href)
    ElMessage.success('分享链接已复制到剪贴板')
  }
}

const closePlayer = () => {
  if (videoPlayer.value) {
    videoPlayer.value.pause()
  }
  playerVisible.value = false
}

const onVideoLoaded = () => {
  ElMessage.success('集锦视频加载完成')
}

const getActionIcon = (actionType: string) => {
  const iconMap: Record<string, any> = {
    shot: Aim,
    assist: Share,
    steal: Shield,
    block: Shield,
    defense: Shield,
    dunk: Basketball,
    three_pointer: Trophy
  }
  return iconMap[actionType] || VideoPlay
}

const formatDuration = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}
</script>

<style lang="scss" scoped>
.player-highlight-card {
  margin-bottom: 24px;
}

.highlight-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
}

// 球员头部
.player-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.player-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .el-icon {
    font-size: 24px;
    color: rgba(255, 255, 255, 0.6);
  }
}

.player-info {
  flex: 1;
}

.player-name {
  color: white;
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.player-meta {
  display: flex;
  gap: 8px;
}

.highlight-actions {
  display: flex;
  gap: 12px;
}

// 集锦预览
.highlight-preview {
  margin-bottom: 24px;
}

.video-thumbnail {
  position: relative;
  height: 200px;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.02);
    
    .play-overlay {
      opacity: 1;
    }
  }
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  
  .el-icon {
    font-size: 48px;
    margin-bottom: 8px;
  }
}

.video-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.play-icon {
  font-size: 64px;
  color: white;
}

// 统计数据
.stats-section {
  margin-bottom: 24px;
}

.stats-title {
  color: white;
  font-size: 1.1rem;
  margin-bottom: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

// 精彩片段
.segments-section {
  margin-bottom: 16px;
}

.segments-title {
  color: white;
  font-size: 1.1rem;
  margin-bottom: 16px;
}

.segments-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.segment-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(4px);
  }
}

.segment-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
}

.segment-info {
  flex: 1;
}

.segment-title {
  color: white;
  font-weight: 500;
  margin-bottom: 4px;
}

.segment-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.segment-duration {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  font-weight: 600;
}

// 视频播放器
.video-player-container {
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.player-details {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.video-info {
  color: white;
  
  h3 {
    margin-bottom: 8px;
  }
  
  p {
    margin: 4px 0;
    color: rgba(255, 255, 255, 0.8);
  }
}

.video-actions {
  display: flex;
  gap: 12px;
}

// 响应式设计
@media (max-width: 768px) {
  .player-header {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .highlight-actions {
    justify-content: center;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .player-details {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .video-actions {
    justify-content: center;
  }
}
</style>
