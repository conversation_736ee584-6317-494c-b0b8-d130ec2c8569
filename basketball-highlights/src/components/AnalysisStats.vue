<template>
  <div class="analysis-stats">
    <el-card class="stats-card">
      <template #header>
        <div class="card-header">
          <el-icon><TrendCharts /></el-icon>
          <span>分析统计总览</span>
        </div>
      </template>

      <!-- 总体统计 -->
      <div class="overall-stats">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-box">
              <div class="stat-icon" style="background: linear-gradient(45deg, #667eea, #764ba2)">
                <el-icon><User /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.totalPlayers }}</div>
                <div class="stat-label">识别球员</div>
              </div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="stat-box">
              <div class="stat-icon" style="background: linear-gradient(45deg, #f093fb, #f5576c)">
                <el-icon><VideoPlay /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.totalHighlights }}</div>
                <div class="stat-label">精彩片段</div>
              </div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="stat-box">
              <div class="stat-icon" style="background: linear-gradient(45deg, #4facfe, #00f2fe)">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ formatDuration(stats.totalDuration) }}</div>
                <div class="stat-label">集锦总时长</div>
              </div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="stat-box">
              <div class="stat-icon" style="background: linear-gradient(45deg, #43e97b, #38f9d7)">
                <el-icon><Trophy /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ stats.averageConfidence.toFixed(1) }}%</div>
                <div class="stat-label">平均置信度</div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 动作类型分布 -->
      <div class="action-distribution">
        <h3 class="section-title">动作类型分布</h3>
        <div class="action-chart">
          <div 
            v-for="action in actionStats" 
            :key="action.type"
            class="action-bar"
          >
            <div class="action-info">
              <span class="action-name">{{ action.name }}</span>
              <span class="action-count">{{ action.count }}</span>
            </div>
            <div class="action-progress">
              <div 
                class="action-fill" 
                :style="{ 
                  width: `${(action.count / maxActionCount) * 100}%`,
                  background: action.color 
                }"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 球员表现排行 -->
      <div class="player-ranking">
        <h3 class="section-title">球员表现排行</h3>
        <div class="ranking-list">
          <div 
            v-for="(player, index) in topPlayers" 
            :key="player.id"
            class="ranking-item"
          >
            <div class="rank-number" :class="getRankClass(index)">
              {{ index + 1 }}
            </div>
            <div class="player-avatar">
              <img v-if="player.avatar" :src="player.avatar" :alt="player.name" />
              <el-icon v-else><User /></el-icon>
            </div>
            <div class="player-info">
              <div class="player-name">{{ player.name }}</div>
              <div class="player-stats">
                {{ player.highlightCount }}个精彩片段 | 置信度 {{ player.avgConfidence.toFixed(1) }}%
              </div>
            </div>
            <div class="player-score">
              <div class="score-number">{{ player.score }}</div>
              <div class="score-label">综合评分</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 时间轴分析 -->
      <div class="timeline-analysis">
        <h3 class="section-title">精彩时刻时间轴</h3>
        <div class="timeline-chart">
          <div class="timeline-bar">
            <div 
              v-for="highlight in timelineHighlights" 
              :key="highlight.id"
              class="timeline-point"
              :style="{ 
                left: `${(highlight.time / videoDuration) * 100}%`,
                background: getActionColor(highlight.type)
              }"
              :title="`${formatTime(highlight.time)} - ${highlight.description}`"
            ></div>
          </div>
          <div class="timeline-labels">
            <span>0:00</span>
            <span>{{ formatTime(videoDuration) }}</span>
          </div>
        </div>
        
        <!-- 图例 -->
        <div class="timeline-legend">
          <div 
            v-for="actionType in uniqueActionTypes" 
            :key="actionType.type"
            class="legend-item"
          >
            <div 
              class="legend-color" 
              :style="{ background: actionType.color }"
            ></div>
            <span class="legend-label">{{ actionType.name }}</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts" name="AnalysisStats">
import { computed } from 'vue'
import { TrendCharts, User, VideoPlay, Clock, Trophy } from '@element-plus/icons-vue'

// 定义 props
interface AnalysisStatsData {
  totalPlayers: number
  totalHighlights: number
  totalDuration: number
  averageConfidence: number
  actionBreakdown: Array<{
    type: string
    name: string
    count: number
  }>
  playerPerformance: Array<{
    id: string
    name: string
    avatar?: string
    highlightCount: number
    avgConfidence: number
    totalTime: number
  }>
  highlights: Array<{
    id: string
    time: number
    type: string
    description: string
  }>
  videoDuration: number
}

const props = defineProps<{
  stats: AnalysisStatsData
}>()

// 计算属性
const actionStats = computed(() => {
  const colors = [
    'linear-gradient(45deg, #667eea, #764ba2)',
    'linear-gradient(45deg, #f093fb, #f5576c)',
    'linear-gradient(45deg, #4facfe, #00f2fe)',
    'linear-gradient(45deg, #43e97b, #38f9d7)',
    'linear-gradient(45deg, #fa709a, #fee140)',
    'linear-gradient(45deg, #a8edea, #fed6e3)'
  ]
  
  return props.stats.actionBreakdown.map((action, index) => ({
    ...action,
    color: colors[index % colors.length]
  }))
})

const maxActionCount = computed(() => {
  return Math.max(...props.stats.actionBreakdown.map(a => a.count))
})

const topPlayers = computed(() => {
  return props.stats.playerPerformance
    .map(player => ({
      ...player,
      score: Math.round(player.highlightCount * 10 + player.avgConfidence * 0.5)
    }))
    .sort((a, b) => b.score - a.score)
    .slice(0, 5)
})

const timelineHighlights = computed(() => {
  return props.stats.highlights.sort((a, b) => a.time - b.time)
})

const uniqueActionTypes = computed(() => {
  const types = [...new Set(props.stats.highlights.map(h => h.type))]
  const actionNames: Record<string, string> = {
    shot: '投篮',
    dunk: '扣篮',
    assist: '助攻',
    steal: '抢断',
    block: '盖帽',
    defense: '防守'
  }
  
  const colors = [
    '#667eea', '#f093fb', '#4facfe', '#43e97b', '#fa709a', '#a8edea'
  ]
  
  return types.map((type, index) => ({
    type,
    name: actionNames[type] || type,
    color: colors[index % colors.length]
  }))
})

const videoDuration = computed(() => props.stats.videoDuration)

// 方法
const formatDuration = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const getRankClass = (index: number) => {
  if (index === 0) return 'rank-gold'
  if (index === 1) return 'rank-silver'
  if (index === 2) return 'rank-bronze'
  return 'rank-normal'
}

const getActionColor = (actionType: string) => {
  const colorMap: Record<string, string> = {
    shot: '#667eea',
    dunk: '#f093fb',
    assist: '#4facfe',
    steal: '#43e97b',
    block: '#fa709a',
    defense: '#a8edea'
  }
  return colorMap[actionType] || '#999'
}
</script>

<style lang="scss" scoped>
.analysis-stats {
  margin-bottom: 30px;
}

.stats-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-weight: 600;
}

// 总体统计
.overall-stats {
  margin-bottom: 40px;
}

.stat-box {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: translateY(-4px);
  }
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  margin-bottom: 4px;
}

.stat-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

// 动作分布
.action-distribution {
  margin-bottom: 40px;
}

.section-title {
  color: white;
  font-size: 1.2rem;
  margin-bottom: 20px;
}

.action-chart {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-bar {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-name {
  color: white;
  font-weight: 500;
}

.action-count {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
}

.action-progress {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.action-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.5s ease;
}

// 球员排行
.player-ranking {
  margin-bottom: 40px;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(4px);
  }
}

.rank-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  color: white;
  
  &.rank-gold {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
  }
  
  &.rank-silver {
    background: linear-gradient(45deg, #c0c0c0, #e8e8e8);
    color: #333;
  }
  
  &.rank-bronze {
    background: linear-gradient(45deg, #cd7f32, #daa520);
    color: white;
  }
  
  &.rank-normal {
    background: rgba(255, 255, 255, 0.2);
  }
}

.player-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.player-info {
  flex: 1;
}

.player-name {
  color: white;
  font-weight: 600;
  margin-bottom: 4px;
}

.player-stats {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

.player-score {
  text-align: center;
}

.score-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.score-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

// 时间轴
.timeline-analysis {
  margin-bottom: 20px;
}

.timeline-chart {
  margin-bottom: 16px;
}

.timeline-bar {
  position: relative;
  height: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  margin-bottom: 8px;
}

.timeline-point {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: translate(-50%, -50%) scale(1.5);
  }
}

.timeline-labels {
  display: flex;
  justify-content: space-between;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

.timeline-legend {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  margin-top: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

// 响应式设计
@media (max-width: 768px) {
  .overall-stats {
    :deep(.el-col) {
      margin-bottom: 16px;
    }
  }
  
  .stat-box {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .ranking-item {
    flex-wrap: wrap;
    gap: 12px;
  }
  
  .timeline-legend {
    justify-content: center;
  }
}
</style>
