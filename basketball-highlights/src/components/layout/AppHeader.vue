<template>
  <header class="app-header">
    <div class="header-container">
      <!-- Logo -->
      <div class="logo" @click="$router.push('/')">
        <div class="logo-icon">🏀</div>
        <span class="logo-text">篮球集锦生成器</span>
      </div>

      <!-- 导航菜单 -->
      <nav class="nav-menu">
        <router-link 
          v-for="item in menuItems" 
          :key="item.path"
          :to="item.path"
          class="nav-item"
          :class="{ active: $route.path === item.path }"
        >
          <el-icon>
            <component :is="item.icon" />
          </el-icon>
          <span>{{ item.label }}</span>
        </router-link>
      </nav>

      <!-- 右侧操作 -->
      <div class="header-actions">
        <!-- 主题切换 -->
        <el-tooltip content="切换主题" placement="bottom">
          <el-button 
            circle 
            @click="toggleTheme"
            class="action-btn"
          >
            <el-icon>
              <Sunny v-if="isDark" />
              <Moon v-else />
            </el-icon>
          </el-button>
        </el-tooltip>

        <!-- 通知 -->
        <el-tooltip content="通知" placement="bottom">
          <el-badge :value="notificationCount" :hidden="notificationCount === 0">
            <el-button 
              circle 
              @click="showNotifications"
              class="action-btn"
            >
              <el-icon><Bell /></el-icon>
            </el-button>
          </el-badge>
        </el-tooltip>

        <!-- 用户菜单 -->
        <el-dropdown @command="handleUserCommand" class="user-dropdown">
          <div class="user-info">
            <el-avatar :size="36" :src="userAvatar">
              <el-icon><User /></el-icon>
            </el-avatar>
            <span class="username">{{ username }}</span>
            <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="profile">
                <el-icon><User /></el-icon>
                个人资料
              </el-dropdown-item>
              <el-dropdown-item command="settings">
                <el-icon><Setting /></el-icon>
                设置
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <!-- 移动端菜单按钮 -->
      <el-button 
        class="mobile-menu-btn"
        @click="toggleMobileMenu"
        circle
      >
        <el-icon><Menu /></el-icon>
      </el-button>
    </div>

    <!-- 移动端菜单 -->
    <div class="mobile-menu" :class="{ active: mobileMenuVisible }">
      <div class="mobile-nav">
        <router-link 
          v-for="item in menuItems" 
          :key="item.path"
          :to="item.path"
          class="mobile-nav-item"
          @click="closeMobileMenu"
        >
          <el-icon>
            <component :is="item.icon" />
          </el-icon>
          <span>{{ item.label }}</span>
        </router-link>
      </div>
    </div>

    <!-- 移动端遮罩 -->
    <div 
      class="mobile-overlay" 
      :class="{ active: mobileMenuVisible }"
      @click="closeMobileMenu"
    ></div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  House, Upload, TrendCharts, Picture, User, Setting,
  Bell, Menu, ArrowDown, SwitchButton, Sunny, Moon
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const isDark = ref(false)
const mobileMenuVisible = ref(false)
const notificationCount = ref(3)
const username = ref('篮球爱好者')
const userAvatar = ref('')

// 菜单项
const menuItems = [
  { path: '/', label: '首页', icon: House },
  { path: '/upload', label: '上传视频', icon: Upload },
  { path: '/gallery', label: '集锦画廊', icon: Picture }
]

// 切换主题
const toggleTheme = () => {
  isDark.value = !isDark.value
  document.documentElement.classList.toggle('dark', isDark.value)
  ElMessage.success(`已切换到${isDark.value ? '深色' : '浅色'}主题`)
}

// 显示通知
const showNotifications = () => {
  ElMessage.info('暂无新通知')
}

// 处理用户菜单命令
const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人资料功能开发中...')
      break
    case 'settings':
      ElMessage.info('设置功能开发中...')
      break
    case 'logout':
      ElMessage.success('已退出登录')
      router.push('/')
      break
  }
}

// 切换移动端菜单
const toggleMobileMenu = () => {
  mobileMenuVisible.value = !mobileMenuVisible.value
}

// 关闭移动端菜单
const closeMobileMenu = () => {
  mobileMenuVisible.value = false
}
</script>

<style lang="scss" scoped>
.app-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// Logo
.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    transform: scale(1.05);
  }
}

.logo-icon {
  font-size: 32px;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-5px);
  }
  60% {
    transform: translateY(-3px);
  }
}

.logo-text {
  font-size: 1.2rem;
  font-weight: 700;
  color: white;
}

// 导航菜单
.nav-menu {
  display: flex;
  gap: 8px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 500;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateY(-2px);
  }
  
  &.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
  }
}

// 右侧操作
.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.action-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
  }
}

// 用户下拉菜单
.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
  }
}

.username {
  color: white;
  font-weight: 500;
  font-size: 14px;
}

.dropdown-icon {
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

// 移动端菜单按钮
.mobile-menu-btn {
  display: none;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

// 移动端菜单
.mobile-menu {
  position: fixed;
  top: 64px;
  left: -300px;
  width: 300px;
  height: calc(100vh - 64px);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  z-index: 999;
  
  &.active {
    left: 0;
  }
}

.mobile-nav {
  padding: 20px;
}

.mobile-nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  margin-bottom: 8px;
  border-radius: 8px;
  color: #333;
  text-decoration: none;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(0, 0, 0, 0.1);
  }
}

// 移动端遮罩
.mobile-overlay {
  position: fixed;
  top: 64px;
  left: 0;
  width: 100vw;
  height: calc(100vh - 64px);
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 998;
  
  &.active {
    opacity: 1;
    visibility: visible;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }
  
  .header-actions {
    gap: 8px;
  }
  
  .username {
    display: none;
  }
  
  .mobile-menu-btn {
    display: flex;
  }
}

@media (max-width: 480px) {
  .logo-text {
    display: none;
  }
  
  .header-container {
    padding: 0 16px;
  }
}

// 深色主题
:global(.dark) {
  .app-header {
    background: rgba(0, 0, 0, 0.8);
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  .mobile-menu {
    background: rgba(0, 0, 0, 0.95);
  }
  
  .mobile-nav-item {
    color: white;
    
    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
  }
}
</style>
