<template>
  <footer class="app-footer">
    <div class="footer-container">
      <!-- 主要内容 -->
      <div class="footer-content">
        <!-- 品牌信息 -->
        <div class="footer-section">
          <div class="footer-logo">
            <div class="logo-icon">🏀</div>
            <span class="logo-text">篮球集锦生成器</span>
          </div>
          <p class="footer-description">
            使用AI技术智能分析篮球视频，
            <br>自动生成个人精彩集锦
          </p>
          <div class="social-links">
            <a href="#" class="social-link">
              <el-icon><Platform /></el-icon>
            </a>
            <a href="#" class="social-link">
              <el-icon><Share /></el-icon>
            </a>
            <a href="#" class="social-link">
              <el-icon><ChatDotRound /></el-icon>
            </a>
          </div>
        </div>

        <!-- 快速链接 -->
        <div class="footer-section">
          <h4 class="section-title">快速链接</h4>
          <ul class="footer-links">
            <li><router-link to="/">首页</router-link></li>
            <li><router-link to="/upload">上传视频</router-link></li>
            <li><router-link to="/gallery">集锦画廊</router-link></li>
            <li><a href="#features">功能介绍</a></li>
          </ul>
        </div>

        <!-- 帮助支持 -->
        <div class="footer-section">
          <h4 class="section-title">帮助支持</h4>
          <ul class="footer-links">
            <li><a href="#help">使用帮助</a></li>
            <li><a href="#faq">常见问题</a></li>
            <li><a href="#contact">联系我们</a></li>
            <li><a href="#feedback">意见反馈</a></li>
          </ul>
        </div>

        <!-- 技术信息 -->
        <div class="footer-section">
          <h4 class="section-title">技术支持</h4>
          <ul class="footer-links">
            <li><a href="#api">API文档</a></li>
            <li><a href="#sdk">SDK下载</a></li>
            <li><a href="#docs">开发文档</a></li>
            <li><a href="#status">服务状态</a></li>
          </ul>
        </div>
      </div>

      <!-- 分隔线 -->
      <div class="footer-divider"></div>

      <!-- 底部信息 -->
      <div class="footer-bottom">
        <div class="copyright">
          <span>© 2024 篮球集锦生成器. 保留所有权利.</span>
        </div>
        <div class="footer-meta">
          <span class="version">v1.0.0</span>
          <span class="separator">|</span>
          <a href="#privacy">隐私政策</a>
          <span class="separator">|</span>
          <a href="#terms">服务条款</a>
          <span class="separator">|</span>
          <span class="build-info">
            构建时间: {{ buildTime }}
          </span>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Platform, Share, ChatDotRound } from '@element-plus/icons-vue'

// 构建时间
const buildTime = ref(new Date().toLocaleString())
</script>

<style lang="scss" scoped>
.app-footer {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: auto;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 40px;
  margin-bottom: 30px;
}

.footer-section {
  h4.section-title {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 16px;
  }
}

// 品牌信息
.footer-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.logo-icon {
  font-size: 28px;
}

.logo-text {
  font-size: 1.2rem;
  font-weight: 700;
  color: white;
}

.footer-description {
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.6;
  margin-bottom: 20px;
}

.social-links {
  display: flex;
  gap: 12px;
}

.social-link {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateY(-2px);
  }
}

// 链接列表
.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
  
  li {
    margin-bottom: 8px;
    
    a {
      color: rgba(255, 255, 255, 0.7);
      text-decoration: none;
      transition: all 0.3s ease;
      
      &:hover {
        color: white;
      }
    }
  }
}

// 分隔线
.footer-divider {
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin: 30px 0 20px;
}

// 底部信息
.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.copyright {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

.footer-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.6);
  
  a {
    color: rgba(255, 255, 255, 0.6);
    text-decoration: none;
    transition: color 0.3s ease;
    
    &:hover {
      color: rgba(255, 255, 255, 0.8);
    }
  }
  
  .separator {
    color: rgba(255, 255, 255, 0.3);
  }
  
  .version {
    background: rgba(255, 255, 255, 0.1);
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
  }
  
  .build-info {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.4);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .footer-bottom {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .footer-meta {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer-container {
    padding: 30px 16px 16px;
  }
  
  .footer-content {
    gap: 24px;
  }
  
  .footer-meta {
    flex-direction: column;
    gap: 8px;
  }
}

// 深色主题
:global(.dark) {
  .app-footer {
    background: rgba(0, 0, 0, 0.9);
  }
}
</style>
