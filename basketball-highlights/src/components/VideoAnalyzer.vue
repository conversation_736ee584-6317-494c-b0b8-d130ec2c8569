<template>
  <div class="video-analyzer">
    <el-card class="analyzer-card">
      <template #header>
        <div class="card-header">
          <el-icon><VideoPlay /></el-icon>
          <span>AI视频分析器</span>
        </div>
      </template>

      <!-- 视频上传区域 -->
      <div v-if="!videoFile" class="upload-section">
        <el-upload
          ref="uploadRef"
          class="video-upload"
          drag
          :auto-upload="false"
          :show-file-list="false"
          accept="video/*"
          @change="handleFileChange"
        >
          <el-icon class="upload-icon"><Upload /></el-icon>
          <div class="upload-text">
            <p>点击或拖拽视频文件到此处</p>
            <p class="upload-hint">支持 MP4、AVI、MOV 格式</p>
          </div>
        </el-upload>
      </div>

      <!-- 视频预览和分析 -->
      <div v-else class="analysis-section">
        <!-- 视频播放器 -->
        <div class="video-container">
          <video
            ref="videoRef"
            :src="videoUrl"
            controls
            width="100%"
            height="300"
            @loadedmetadata="onVideoLoaded"
          >
            您的浏览器不支持视频播放
          </video>
          
          <!-- 分析覆盖层 -->
          <canvas
            ref="canvasRef"
            class="analysis-overlay"
            :class="{ active: isAnalyzing }"
          ></canvas>
        </div>

        <!-- 分析控制 -->
        <div class="analysis-controls">
          <div class="control-group">
            <el-button
              type="primary"
              :loading="isAnalyzing"
              :disabled="!videoLoaded"
              @click="startAnalysis"
            >
              <el-icon><TrendCharts /></el-icon>
              {{ isAnalyzing ? '分析中...' : '开始AI分析' }}
            </el-button>
            
            <el-button
              v-if="isAnalyzing"
              type="danger"
              @click="stopAnalysis"
            >
              <el-icon><Close /></el-icon>
              停止分析
            </el-button>
            
            <el-button @click="resetAnalysis">
              <el-icon><Refresh /></el-icon>
              重新选择
            </el-button>
          </div>

          <!-- 分析选项 -->
          <div class="analysis-options">
            <el-form :model="analysisConfig" label-width="100px" size="small">
              <el-form-item label="分析精度">
                <el-radio-group v-model="analysisConfig.precision">
                  <el-radio value="fast">快速</el-radio>
                  <el-radio value="standard">标准</el-radio>
                  <el-radio value="high">高精度</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item label="检测功能">
                <el-checkbox-group v-model="analysisConfig.features">
                  <el-checkbox value="pose">姿态检测</el-checkbox>
                  <el-checkbox value="object">目标检测</el-checkbox>
                  <el-checkbox value="action">动作识别</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 分析进度 -->
        <div v-if="isAnalyzing" class="analysis-progress">
          <el-progress
            :percentage="analysisProgress"
            :status="analysisProgress === 100 ? 'success' : undefined"
          >
            <template #default="{ percentage }">
              <span class="progress-text">{{ percentage }}%</span>
            </template>
          </el-progress>
          <p class="progress-info">{{ currentStep }}</p>
        </div>

        <!-- 分析结果 -->
        <div v-if="analysisResults" class="analysis-results">
          <h3>分析结果</h3>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-statistic title="检测到的球员" :value="analysisResults.players.length" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="精彩片段" :value="analysisResults.highlights.length" />
            </el-col>
            <el-col :span="8">
              <el-statistic title="平均置信度" :value="analysisResults.averageConfidence" suffix="%" />
            </el-col>
          </el-row>

          <!-- 检测到的动作 -->
          <div class="detected-actions">
            <h4>检测到的动作</h4>
            <div class="action-tags">
              <el-tag
                v-for="action in analysisResults.detectedActions"
                :key="action.type"
                :type="getActionTagType(action.type)"
                class="action-tag"
              >
                {{ action.name }} ({{ action.count }})
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts" name="VideoAnalyzer">
import { ref, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { VideoPlay, Upload, TrendCharts, Close, Refresh } from '@element-plus/icons-vue'
import { videoAnalysisService } from '../services/videoAnalysis'
import type { UploadFile } from 'element-plus'

// 响应式数据
const videoFile = ref<File | null>(null)
const videoUrl = ref('')
const videoLoaded = ref(false)
const isAnalyzing = ref(false)
const analysisProgress = ref(0)
const currentStep = ref('')
const analysisResults = ref<any>(null)

// 元素引用
const videoRef = ref<HTMLVideoElement>()
const canvasRef = ref<HTMLCanvasElement>()
const uploadRef = ref()

// 分析配置
const analysisConfig = ref({
  precision: 'standard' as 'fast' | 'standard' | 'high',
  features: ['pose', 'object', 'action']
})

// 处理文件选择
const handleFileChange = (file: UploadFile) => {
  if (file.raw) {
    videoFile.value = file.raw
    videoUrl.value = URL.createObjectURL(file.raw)
    ElMessage.success('视频文件加载成功')
  }
}

// 视频加载完成
const onVideoLoaded = () => {
  videoLoaded.value = true
  setupCanvas()
  ElMessage.info('视频已准备就绪，可以开始分析')
}

// 设置画布
const setupCanvas = () => {
  if (!videoRef.value || !canvasRef.value) return
  
  const video = videoRef.value
  const canvas = canvasRef.value
  
  canvas.width = video.videoWidth
  canvas.height = video.videoHeight
  canvas.style.width = '100%'
  canvas.style.height = '300px'
}

// 开始分析
const startAnalysis = async () => {
  if (!videoRef.value || !canvasRef.value) {
    ElMessage.error('视频或画布未准备就绪')
    return
  }

  try {
    isAnalyzing.value = true
    analysisProgress.value = 0
    analysisResults.value = null
    
    ElMessage.info('开始AI视频分析...')
    
    // 分析视频
    const results = await videoAnalysisService.analyzeVideo(
      videoRef.value,
      {
        precision: analysisConfig.value.precision,
        features: analysisConfig.value.features,
        outputFormat: 'mp4',
        quality: 7
      },
      (progress, step) => {
        analysisProgress.value = progress
        currentStep.value = step
        
        // 在画布上绘制分析进度
        drawAnalysisOverlay(progress)
      }
    )
    
    // 处理分析结果
    analysisResults.value = {
      players: results.players,
      highlights: results.highlights,
      averageConfidence: results.highlights.reduce((sum, h) => sum + h.confidence, 0) / results.highlights.length || 0,
      detectedActions: getDetectedActions(results.highlights)
    }
    
    ElMessage.success('视频分析完成！')
    
  } catch (error) {
    console.error('分析失败:', error)
    ElMessage.error('分析失败: ' + (error as Error).message)
  } finally {
    isAnalyzing.value = false
  }
}

// 停止分析
const stopAnalysis = () => {
  isAnalyzing.value = false
  analysisProgress.value = 0
  currentStep.value = ''
  ElMessage.info('分析已停止')
}

// 重置分析
const resetAnalysis = () => {
  videoFile.value = null
  videoUrl.value = ''
  videoLoaded.value = false
  isAnalyzing.value = false
  analysisProgress.value = 0
  currentStep.value = ''
  analysisResults.value = null
  
  if (videoUrl.value) {
    URL.revokeObjectURL(videoUrl.value)
  }
}

// 在画布上绘制分析覆盖层
const drawAnalysisOverlay = (progress: number) => {
  if (!canvasRef.value) return
  
  const canvas = canvasRef.value
  const ctx = canvas.getContext('2d')
  if (!ctx) return
  
  // 清除画布
  ctx.clearRect(0, 0, canvas.width, canvas.height)
  
  // 绘制半透明覆盖层
  ctx.fillStyle = 'rgba(0, 0, 0, 0.3)'
  ctx.fillRect(0, 0, canvas.width, canvas.height)
  
  // 绘制进度条
  const barWidth = canvas.width * 0.6
  const barHeight = 20
  const barX = (canvas.width - barWidth) / 2
  const barY = canvas.height / 2 - barHeight / 2
  
  // 进度条背景
  ctx.fillStyle = 'rgba(255, 255, 255, 0.3)'
  ctx.fillRect(barX, barY, barWidth, barHeight)
  
  // 进度条填充
  ctx.fillStyle = '#409eff'
  ctx.fillRect(barX, barY, (barWidth * progress) / 100, barHeight)
  
  // 进度文字
  ctx.fillStyle = 'white'
  ctx.font = '16px Arial'
  ctx.textAlign = 'center'
  ctx.fillText(`分析中... ${progress.toFixed(0)}%`, canvas.width / 2, barY - 10)
}

// 获取检测到的动作统计
const getDetectedActions = (highlights: any[]) => {
  const actionCounts: Record<string, number> = {}
  const actionNames: Record<string, string> = {
    shot: '投篮',
    assist: '助攻',
    defense: '防守',
    dunk: '扣篮',
    steal: '抢断',
    block: '盖帽'
  }
  
  highlights.forEach(highlight => {
    const type = highlight.type
    actionCounts[type] = (actionCounts[type] || 0) + 1
  })
  
  return Object.entries(actionCounts).map(([type, count]) => ({
    type,
    name: actionNames[type] || type,
    count
  }))
}

// 获取动作标签类型
const getActionTagType = (actionType: string) => {
  const typeMap: Record<string, string> = {
    shot: 'primary',
    assist: 'success',
    defense: 'warning',
    dunk: 'danger',
    steal: 'info',
    block: 'primary'
  }
  return typeMap[actionType] || 'default'
}
</script>

<style lang="scss" scoped>
.video-analyzer {
  max-width: 800px;
  margin: 0 auto;
}

.analyzer-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-weight: 600;
}

// 上传区域
.upload-section {
  padding: 40px 20px;
}

.video-upload {
  :deep(.el-upload-dragger) {
    background: rgba(255, 255, 255, 0.05);
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 60px 40px;
    
    &:hover {
      border-color: rgba(255, 255, 255, 0.6);
      background: rgba(255, 255, 255, 0.1);
    }
  }
}

.upload-icon {
  font-size: 4rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 20px;
}

.upload-text {
  color: white;
  
  p {
    margin: 8px 0;
  }
  
  .upload-hint {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.7);
  }
}

// 分析区域
.analysis-section {
  padding: 20px;
}

.video-container {
  position: relative;
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
}

.analysis-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
  
  &.active {
    opacity: 1;
  }
}

.analysis-controls {
  margin-bottom: 20px;
}

.control-group {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
  justify-content: center;
}

.analysis-options {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: 16px;
  
  :deep(.el-form-item__label) {
    color: rgba(255, 255, 255, 0.9);
  }
  
  :deep(.el-radio__label),
  :deep(.el-checkbox__label) {
    color: rgba(255, 255, 255, 0.8);
  }
}

.analysis-progress {
  margin-bottom: 20px;
  text-align: center;
}

.progress-text {
  color: white;
  font-weight: 600;
}

.progress-info {
  color: rgba(255, 255, 255, 0.8);
  margin-top: 8px;
  font-size: 14px;
}

// 分析结果
.analysis-results {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 20px;
  
  h3, h4 {
    color: white;
    margin-bottom: 16px;
  }
  
  :deep(.el-statistic__head) {
    color: rgba(255, 255, 255, 0.8);
  }
  
  :deep(.el-statistic__content) {
    color: white;
  }
}

.detected-actions {
  margin-top: 20px;
}

.action-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-tag {
  margin-bottom: 8px;
}

// 响应式设计
@media (max-width: 768px) {
  .control-group {
    flex-direction: column;
    align-items: center;
  }
  
  .analysis-options {
    :deep(.el-form-item) {
      margin-bottom: 12px;
    }
  }
}
</style>
