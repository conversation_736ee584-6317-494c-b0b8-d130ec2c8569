// 全局样式重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: #333;
  background: #f5f5f5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 链接样式
a {
  color: inherit;
  text-decoration: none;
  transition: all 0.3s ease;
}

// 按钮样式
button {
  cursor: pointer;
  border: none;
  outline: none;
  transition: all 0.3s ease;
}

// 输入框样式
input, textarea, select {
  outline: none;
  border: none;
  font-family: inherit;
}

// 图片样式
img {
  max-width: 100%;
  height: auto;
  display: block;
}

// 列表样式
ul, ol {
  list-style: none;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

// Element Plus 组件样式覆盖
.el-card {
  border: none;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  
  .el-card__header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 20px;
  }
  
  .el-card__body {
    padding: 20px;
  }
}

.el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
  }
  
  &.is-circle {
    &:hover {
      transform: scale(1.1);
    }
  }
}

.el-input {
  .el-input__wrapper {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    &.is-focus {
      box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
    }
  }
}

.el-select {
  .el-select__wrapper {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.el-progress {
  .el-progress__text {
    font-weight: 600;
  }
}

.el-timeline {
  .el-timeline-item__timestamp {
    color: rgba(255, 255, 255, 0.6);
  }
  
  .el-timeline-item__wrapper {
    .el-timeline-item__tail {
      border-left: 2px solid rgba(255, 255, 255, 0.2);
    }
  }
}

.el-upload {
  .el-upload-dragger {
    border-radius: 12px;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: var(--el-color-primary);
    }
  }
}

.el-message {
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.el-notification {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

// 工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.overflow-hidden {
  overflow: hidden;
}

.cursor-pointer {
  cursor: pointer;
}

// 间距工具类
@for $i from 0 through 20 {
  .m-#{$i} {
    margin: #{$i * 4}px;
  }
  
  .mt-#{$i} {
    margin-top: #{$i * 4}px;
  }
  
  .mb-#{$i} {
    margin-bottom: #{$i * 4}px;
  }
  
  .ml-#{$i} {
    margin-left: #{$i * 4}px;
  }
  
  .mr-#{$i} {
    margin-right: #{$i * 4}px;
  }
  
  .mx-#{$i} {
    margin-left: #{$i * 4}px;
    margin-right: #{$i * 4}px;
  }
  
  .my-#{$i} {
    margin-top: #{$i * 4}px;
    margin-bottom: #{$i * 4}px;
  }
  
  .p-#{$i} {
    padding: #{$i * 4}px;
  }
  
  .pt-#{$i} {
    padding-top: #{$i * 4}px;
  }
  
  .pb-#{$i} {
    padding-bottom: #{$i * 4}px;
  }
  
  .pl-#{$i} {
    padding-left: #{$i * 4}px;
  }
  
  .pr-#{$i} {
    padding-right: #{$i * 4}px;
  }
  
  .px-#{$i} {
    padding-left: #{$i * 4}px;
    padding-right: #{$i * 4}px;
  }
  
  .py-#{$i} {
    padding-top: #{$i * 4}px;
    padding-bottom: #{$i * 4}px;
  }
}

// 动画类
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.5s ease-out;
}

.bounce-in {
  animation: bounceIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// 深色主题
.dark {
  color-scheme: dark;
  
  body {
    background: #1a1a1a;
    color: #e5e5e5;
  }
  
  .el-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    
    .el-card__header {
      border-bottom-color: rgba(255, 255, 255, 0.1);
    }
  }
  
  .el-input {
    .el-input__wrapper {
      background: rgba(255, 255, 255, 0.05);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
  }
  
  .el-select {
    .el-select__wrapper {
      background: rgba(255, 255, 255, 0.05);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
  }
}

// 响应式断点
$breakpoints: (
  xs: 480px,
  sm: 768px,
  md: 1024px,
  lg: 1200px,
  xl: 1440px
);

@each $name, $size in $breakpoints {
  @media (max-width: $size) {
    .hidden-#{$name} {
      display: none !important;
    }
  }
}

// 打印样式
@media print {
  .no-print {
    display: none !important;
  }
  
  * {
    color: #000 !important;
    background: #fff !important;
  }
}
