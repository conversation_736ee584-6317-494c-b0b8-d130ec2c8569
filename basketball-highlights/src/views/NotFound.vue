<template>
  <div class="not-found-page">
    <div class="container">
      <div class="not-found-content">
        <!-- 404动画 -->
        <div class="error-animation">
          <div class="basketball-container">
            <div class="basketball">🏀</div>
            <div class="hoop">🥅</div>
          </div>
          <div class="error-code">404</div>
        </div>

        <!-- 错误信息 -->
        <div class="error-info">
          <h1 class="error-title">页面未找到</h1>
          <p class="error-description">
            抱歉，您访问的页面不存在或已被移动。
            <br>
            让我们帮您找到正确的方向！
          </p>
        </div>

        <!-- 操作按钮 -->
        <div class="error-actions">
          <el-button 
            type="primary" 
            size="large"
            @click="goHome"
          >
            <el-icon><House /></el-icon>
            返回首页
          </el-button>
          
          <el-button 
            size="large"
            @click="goBack"
          >
            <el-icon><ArrowLeft /></el-icon>
            返回上页
          </el-button>
          
          <el-button 
            size="large"
            @click="$router.push('/upload')"
          >
            <el-icon><Upload /></el-icon>
            上传视频
          </el-button>
        </div>

        <!-- 推荐链接 -->
        <div class="suggested-links">
          <h3 class="suggestions-title">您可能想要：</h3>
          <div class="links-grid">
            <router-link 
              v-for="link in suggestedLinks" 
              :key="link.path"
              :to="link.path"
              class="suggestion-link"
            >
              <div class="link-icon">
                <component :is="link.icon" />
              </div>
              <div class="link-content">
                <div class="link-title">{{ link.title }}</div>
                <div class="link-description">{{ link.description }}</div>
              </div>
            </router-link>
          </div>
        </div>

        <!-- 搜索建议 -->
        <div class="search-section">
          <h3 class="search-title">或者搜索您需要的内容：</h3>
          <div class="search-box">
            <el-input
              v-model="searchQuery"
              placeholder="搜索球员、视频或功能..."
              size="large"
              @keyup.enter="performSearch"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
              <template #append>
                <el-button @click="performSearch">搜索</el-button>
              </template>
            </el-input>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  House, ArrowLeft, Upload, Search, VideoPlay, 
  Picture, TrendCharts, User 
} from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const searchQuery = ref('')

// 推荐链接
const suggestedLinks = [
  {
    path: '/',
    title: '首页',
    description: '了解我们的AI篮球分析平台',
    icon: House
  },
  {
    path: '/upload',
    title: '上传视频',
    description: '开始分析您的篮球视频',
    icon: Upload
  },
  {
    path: '/gallery',
    title: '集锦画廊',
    description: '浏览精彩的篮球集锦',
    icon: Picture
  }
]

// 方法
const goHome = () => {
  router.push('/')
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}

const performSearch = () => {
  if (searchQuery.value.trim()) {
    ElMessage.info(`搜索功能开发中: "${searchQuery.value}"`)
    // 这里可以实现搜索逻辑
  } else {
    ElMessage.warning('请输入搜索关键词')
  }
}
</script>

<style lang="scss" scoped>
.not-found-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.container {
  max-width: 800px;
  width: 100%;
}

.not-found-content {
  text-align: center;
}

// 404动画
.error-animation {
  margin-bottom: 40px;
  position: relative;
}

.basketball-container {
  position: relative;
  height: 120px;
  margin-bottom: 20px;
}

.basketball {
  font-size: 60px;
  animation: bounce 2s infinite;
  display: inline-block;
}

.hoop {
  position: absolute;
  right: 20%;
  top: 20px;
  font-size: 40px;
  animation: sway 3s ease-in-out infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) rotate(0deg);
  }
  40% {
    transform: translateY(-30px) rotate(180deg);
  }
  60% {
    transform: translateY(-15px) rotate(270deg);
  }
}

@keyframes sway {
  0%, 100% {
    transform: rotate(-5deg);
  }
  50% {
    transform: rotate(5deg);
  }
}

.error-code {
  font-size: 6rem;
  font-weight: 900;
  color: white;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  margin-bottom: 20px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

// 错误信息
.error-info {
  margin-bottom: 40px;
}

.error-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 16px;
}

.error-description {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  max-width: 500px;
  margin: 0 auto;
}

// 操作按钮
.error-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 60px;
  flex-wrap: wrap;
}

// 推荐链接
.suggested-links {
  margin-bottom: 40px;
}

.suggestions-title {
  font-size: 1.3rem;
  color: white;
  margin-bottom: 24px;
}

.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  max-width: 600px;
  margin: 0 auto;
}

.suggestion-link {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  text-decoration: none;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  }
}

.link-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.link-content {
  flex: 1;
  text-align: left;
}

.link-title {
  font-size: 1rem;
  font-weight: 600;
  color: white;
  margin-bottom: 4px;
}

.link-description {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.4;
}

// 搜索区域
.search-section {
  max-width: 500px;
  margin: 0 auto;
}

.search-title {
  font-size: 1.2rem;
  color: white;
  margin-bottom: 20px;
}

.search-box {
  :deep(.el-input-group__append) {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-color: rgba(255, 255, 255, 0.2);
    
    .el-button {
      background: transparent;
      border: none;
      color: white;
      font-weight: 600;
    }
  }
  
  :deep(.el-input__wrapper) {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    
    .el-input__inner {
      color: white;
      
      &::placeholder {
        color: rgba(255, 255, 255, 0.6);
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .error-code {
    font-size: 4rem;
  }
  
  .error-title {
    font-size: 2rem;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .links-grid {
    grid-template-columns: 1fr;
  }
  
  .suggestion-link {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .link-content {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .error-code {
    font-size: 3rem;
  }
  
  .error-title {
    font-size: 1.5rem;
  }
  
  .basketball {
    font-size: 40px;
  }
  
  .hoop {
    font-size: 30px;
  }
}

// 深色主题适配
:global(.dark) {
  .suggestion-link {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.1);
    
    &:hover {
      background: rgba(0, 0, 0, 0.4);
    }
  }
}
</style>
