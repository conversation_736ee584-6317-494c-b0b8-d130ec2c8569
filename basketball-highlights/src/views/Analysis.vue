<template>
  <div class="analysis-page">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">
          <el-icon><TrendCharts /></el-icon>
          视频分析
        </h1>
        <p class="page-subtitle">AI正在智能分析您的篮球视频</p>
      </div>

      <!-- 分析进度 -->
      <div class="progress-section">
        <el-card class="progress-card">
          <div class="progress-content">
            <div class="progress-visual">
              <el-progress 
                type="circle" 
                :percentage="analysisProgress" 
                :width="120"
                :stroke-width="8"
                :color="progressColor"
              >
                <template #default="{ percentage }">
                  <span class="progress-text">{{ percentage }}%</span>
                </template>
              </el-progress>
            </div>
            <div class="progress-info">
              <h3 class="current-step">{{ currentStep }}</h3>
              <p class="step-description">{{ stepDescription }}</p>
              <div class="time-info">
                <span>已用时间: {{ formatDuration(elapsedTime) }}</span>
                <span v-if="estimatedTime">预计剩余: {{ formatDuration(estimatedTime) }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 分析步骤 -->
      <div class="steps-section">
        <el-card class="steps-card">
          <template #header>
            <div class="card-header">
              <el-icon><List /></el-icon>
              <span>分析步骤</span>
            </div>
          </template>
          
          <el-timeline>
            <el-timeline-item
              v-for="(step, index) in analysisSteps"
              :key="step.id"
              :type="getStepType(step.status)"
              :icon="getStepIcon(step.status)"
              :timestamp="step.timestamp"
            >
              <div class="step-content">
                <h4 class="step-title">{{ step.title }}</h4>
                <p class="step-desc">{{ step.description }}</p>
                <div v-if="step.details" class="step-details">
                  <el-tag 
                    v-for="detail in step.details" 
                    :key="detail"
                    size="small"
                    class="detail-tag"
                  >
                    {{ detail }}
                  </el-tag>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </div>

      <!-- 实时结果预览 -->
      <div class="preview-section" v-if="previewData">
        <el-row :gutter="20">
          <!-- 球员识别 -->
          <el-col :span="8">
            <el-card class="preview-card">
              <template #header>
                <div class="card-header">
                  <el-icon><User /></el-icon>
                  <span>球员识别</span>
                </div>
              </template>
              <div class="player-preview">
                <div class="player-count">
                  <span class="count-number">{{ previewData.playerCount }}</span>
                  <span class="count-label">位球员</span>
                </div>
                <div class="player-list">
                  <div 
                    v-for="player in previewData.players.slice(0, 3)" 
                    :key="player.id"
                    class="player-item"
                  >
                    <div class="player-avatar">
                      <img v-if="player.avatar" :src="player.avatar" :alt="player.name" />
                      <el-icon v-else><User /></el-icon>
                    </div>
                    <div class="player-info">
                      <div class="player-name">{{ player.name || '球员' + player.number }}</div>
                      <div class="player-confidence">置信度: {{ player.confidence }}%</div>
                    </div>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>

          <!-- 动作识别 -->
          <el-col :span="8">
            <el-card class="preview-card">
              <template #header>
                <div class="card-header">
                  <el-icon><VideoPlay /></el-icon>
                  <span>动作识别</span>
                </div>
              </template>
              <div class="action-preview">
                <div class="action-count">
                  <span class="count-number">{{ previewData.actionCount }}</span>
                  <span class="count-label">个动作</span>
                </div>
                <div class="action-types">
                  <div 
                    v-for="action in previewData.actionTypes" 
                    :key="action.type"
                    class="action-type"
                  >
                    <span class="action-name">{{ action.name }}</span>
                    <span class="action-count">{{ action.count }}</span>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>

          <!-- 精彩片段 -->
          <el-col :span="8">
            <el-card class="preview-card">
              <template #header>
                <div class="card-header">
                  <el-icon><Star /></el-icon>
                  <span>精彩片段</span>
                </div>
              </template>
              <div class="highlight-preview">
                <div class="highlight-count">
                  <span class="count-number">{{ previewData.highlightCount }}</span>
                  <span class="count-label">个片段</span>
                </div>
                <div class="highlight-quality">
                  <el-progress 
                    :percentage="previewData.averageQuality" 
                    :show-text="false"
                    :stroke-width="6"
                  />
                  <span class="quality-text">平均质量: {{ previewData.averageQuality }}%</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 操作按钮 -->
      <div class="actions">
        <el-button size="large" @click="$router.go(-1)">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <el-button 
          v-if="isCompleted"
          type="primary" 
          size="large" 
          @click="viewResults"
        >
          <el-icon><View /></el-icon>
          查看结果
        </el-button>
        <el-button 
          v-if="isAnalyzing"
          type="danger" 
          size="large" 
          @click="cancelAnalysis"
        >
          <el-icon><Close /></el-icon>
          取消分析
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  TrendCharts, List, User, VideoPlay, Star, ArrowLeft, 
  View, Close, Check, Loading, Warning 
} from '@element-plus/icons-vue'
import { useAnalysisStore } from '@/stores'

const route = useRoute()
const router = useRouter()
const analysisStore = useAnalysisStore()

// 分析状态
const analysisProgress = ref(0)
const currentStep = ref('准备开始分析...')
const stepDescription = ref('正在初始化分析环境')
const elapsedTime = ref(0)
const estimatedTime = ref(0)
const isAnalyzing = ref(false)
const isCompleted = ref(false)

// 分析步骤
const analysisSteps = ref([
  {
    id: 1,
    title: '视频预处理',
    description: '解析视频格式，提取关键帧',
    status: 'pending',
    timestamp: '',
    details: []
  },
  {
    id: 2,
    title: '球员检测',
    description: '使用AI模型识别视频中的球员',
    status: 'pending',
    timestamp: '',
    details: []
  },
  {
    id: 3,
    title: '动作识别',
    description: '分析球员动作，识别投篮、传球等行为',
    status: 'pending',
    timestamp: '',
    details: []
  },
  {
    id: 4,
    title: '数据统计',
    description: '计算球员数据和比赛统计',
    status: 'pending',
    timestamp: '',
    details: []
  },
  {
    id: 5,
    title: '生成集锦',
    description: '自动剪辑精彩片段，生成个人集锦',
    status: 'pending',
    timestamp: '',
    details: []
  }
])

// 预览数据
const previewData = ref<any>(null)

// 定时器
let progressTimer: number | null = null
let timeTimer: number | null = null

// 计算属性
const progressColor = computed(() => {
  if (analysisProgress.value < 30) return '#f56c6c'
  if (analysisProgress.value < 70) return '#e6a23c'
  return '#67c23a'
})

// 获取步骤类型
const getStepType = (status: string) => {
  const types: Record<string, string> = {
    'pending': 'info',
    'processing': 'warning',
    'completed': 'success',
    'failed': 'danger'
  }
  return types[status] || 'info'
}

// 获取步骤图标
const getStepIcon = (status: string) => {
  const icons: Record<string, any> = {
    'pending': Loading,
    'processing': Loading,
    'completed': Check,
    'failed': Warning
  }
  return icons[status] || Loading
}

// 格式化时间
const formatDuration = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// 模拟分析进度
const simulateAnalysis = () => {
  isAnalyzing.value = true
  let currentStepIndex = 0
  
  progressTimer = setInterval(() => {
    if (analysisProgress.value < 100) {
      analysisProgress.value += Math.random() * 3
      
      // 更新当前步骤
      const stepProgress = analysisProgress.value / 20
      if (stepProgress > currentStepIndex && currentStepIndex < analysisSteps.value.length) {
        // 完成当前步骤
        if (currentStepIndex > 0) {
          analysisSteps.value[currentStepIndex - 1].status = 'completed'
          analysisSteps.value[currentStepIndex - 1].timestamp = new Date().toLocaleTimeString()
        }
        
        // 开始下一步骤
        if (currentStepIndex < analysisSteps.value.length) {
          analysisSteps.value[currentStepIndex].status = 'processing'
          currentStep.value = analysisSteps.value[currentStepIndex].title
          stepDescription.value = analysisSteps.value[currentStepIndex].description
          currentStepIndex++
        }
      }
      
      // 更新预览数据
      updatePreviewData()
    } else {
      // 分析完成
      analysisProgress.value = 100
      isAnalyzing.value = false
      isCompleted.value = true
      currentStep.value = '分析完成'
      stepDescription.value = '所有分析步骤已完成，可以查看结果'
      
      // 完成最后一个步骤
      if (currentStepIndex > 0) {
        analysisSteps.value[currentStepIndex - 1].status = 'completed'
        analysisSteps.value[currentStepIndex - 1].timestamp = new Date().toLocaleTimeString()
      }
      
      if (progressTimer) {
        clearInterval(progressTimer)
        progressTimer = null
      }
      
      ElMessage.success('视频分析完成!')
    }
  }, 500)
  
  // 时间计时器
  timeTimer = setInterval(() => {
    elapsedTime.value++
    if (analysisProgress.value > 0) {
      const totalEstimated = (elapsedTime.value / analysisProgress.value) * 100
      estimatedTime.value = Math.max(0, Math.round(totalEstimated - elapsedTime.value))
    }
  }, 1000)
}

// 更新预览数据
const updatePreviewData = () => {
  const progress = analysisProgress.value
  
  previewData.value = {
    playerCount: Math.min(10, Math.floor(progress / 10)),
    players: [
      { id: '1', name: '球员1', number: '23', confidence: 95, avatar: null },
      { id: '2', name: '球员2', number: '6', confidence: 88, avatar: null },
      { id: '3', name: '球员3', number: '11', confidence: 92, avatar: null }
    ],
    actionCount: Math.floor(progress * 2),
    actionTypes: [
      { type: 'shot', name: '投篮', count: Math.floor(progress / 5) },
      { type: 'pass', name: '传球', count: Math.floor(progress / 3) },
      { type: 'dribble', name: '运球', count: Math.floor(progress / 2) }
    ],
    highlightCount: Math.floor(progress / 8),
    averageQuality: Math.min(100, Math.floor(progress * 0.8 + 20))
  }
}

// 查看结果
const viewResults = () => {
  router.push(`/highlights/${route.params.id}`)
}

// 取消分析
const cancelAnalysis = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要取消当前分析吗？已分析的数据将会丢失。',
      '取消分析',
      {
        confirmButtonText: '确定',
        cancelButtonText: '继续分析',
        type: 'warning'
      }
    )
    
    // 停止定时器
    if (progressTimer) {
      clearInterval(progressTimer)
      progressTimer = null
    }
    if (timeTimer) {
      clearInterval(timeTimer)
      timeTimer = null
    }
    
    router.go(-1)
  } catch {
    // 用户取消
  }
}

onMounted(() => {
  // 开始模拟分析
  setTimeout(() => {
    simulateAnalysis()
  }, 1000)
})

onUnmounted(() => {
  if (progressTimer) {
    clearInterval(progressTimer)
  }
  if (timeTimer) {
    clearInterval(timeTimer)
  }
})
</script>

<style lang="scss" scoped>
.analysis-page {
  min-height: 100vh;
  padding: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.page-subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
}

.progress-section {
  margin-bottom: 40px;
}

.progress-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.progress-content {
  display: flex;
  align-items: center;
  gap: 40px;
  padding: 20px;
}

.progress-visual {
  flex-shrink: 0;
}

.progress-text {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.progress-info {
  flex: 1;
}

.current-step {
  font-size: 1.5rem;
  color: white;
  margin-bottom: 10px;
}

.step-description {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 15px;
}

.time-info {
  display: flex;
  gap: 30px;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

.steps-section {
  margin-bottom: 40px;
}

.steps-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-weight: 600;
}

.step-content {
  padding: 10px 0;
}

.step-title {
  color: white;
  margin-bottom: 5px;
}

.step-desc {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 10px;
}

.step-details {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.detail-tag {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
}

.preview-section {
  margin-bottom: 40px;
}

.preview-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  height: 100%;
}

.player-preview,
.action-preview,
.highlight-preview {
  padding: 20px 0;
}

.count-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-right: 8px;
}

.count-label {
  color: rgba(255, 255, 255, 0.7);
}

.player-list {
  margin-top: 20px;
}

.player-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.player-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.player-info {
  flex: 1;
}

.player-name {
  color: white;
  font-size: 14px;
  margin-bottom: 2px;
}

.player-confidence {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

.action-types {
  margin-top: 20px;
}

.action-type {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.action-name {
  color: rgba(255, 255, 255, 0.8);
}

.action-count {
  color: white;
  font-weight: 600;
}

.highlight-quality {
  margin-top: 20px;
}

.quality-text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  margin-top: 8px;
  display: block;
}

.actions {
  display: flex;
  justify-content: center;
  gap: 20px;
}

@media (max-width: 768px) {
  .progress-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .preview-section {
    :deep(.el-col) {
      margin-bottom: 20px;
    }
  }
  
  .actions {
    flex-direction: column;
    align-items: center;
  }
}
</style>
