<template>
  <div class="upload-page">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">
          <el-icon><Upload /></el-icon>
          视频上传
        </h1>
        <p class="page-subtitle">上传篮球比赛视频，开始AI智能分析</p>
      </div>

      <!-- 上传区域 -->
      <div class="upload-section">
        <el-card class="upload-card">
          <!-- 拖拽上传 -->
          <el-upload
            ref="uploadRef"
            class="upload-dragger"
            drag
            :action="uploadUrl"
            :before-upload="beforeUpload"
            :on-progress="onProgress"
            :on-success="onSuccess"
            :on-error="onError"
            :show-file-list="false"
            accept="video/*"
          >
            <div class="upload-content">
              <el-icon class="upload-icon" v-if="!uploading">
                <VideoPlay />
              </el-icon>
              <div v-if="uploading" class="upload-progress">
                <el-progress 
                  type="circle" 
                  :percentage="uploadProgress" 
                  :width="80"
                  :stroke-width="6"
                />
              </div>
              <div class="upload-text">
                <p class="upload-title" v-if="!uploading">
                  点击或拖拽视频文件到此区域上传
                </p>
                <p class="upload-title" v-else>
                  正在上传... {{ uploadProgress }}%
                </p>
                <p class="upload-hint">
                  支持 MP4、AVI、MOV 格式，文件大小不超过 2GB
                </p>
              </div>
            </div>
          </el-upload>

          <!-- 上传状态 -->
          <div v-if="currentFile" class="file-info">
            <div class="file-details">
              <el-icon><Document /></el-icon>
              <div class="file-meta">
                <div class="file-name">{{ currentFile.name }}</div>
                <div class="file-size">{{ formatFileSize(currentFile.size) }}</div>
              </div>
              <el-button 
                v-if="!uploading" 
                type="danger" 
                text 
                @click="removeFile"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </div>
        </el-card>

        <!-- 上传选项 -->
        <el-card class="options-card">
          <template #header>
            <div class="card-header">
              <el-icon><Setting /></el-icon>
              <span>分析选项</span>
            </div>
          </template>

          <el-form :model="analysisOptions" label-width="120px">
            <el-form-item label="分析精度">
              <el-radio-group v-model="analysisOptions.precision">
                <el-radio value="fast">快速分析</el-radio>
                <el-radio value="standard">标准分析</el-radio>
                <el-radio value="high">高精度分析</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="识别内容">
              <el-checkbox-group v-model="analysisOptions.features">
                <el-checkbox value="players">球员识别</el-checkbox>
                <el-checkbox value="actions">动作识别</el-checkbox>
                <el-checkbox value="stats">数据统计</el-checkbox>
                <el-checkbox value="highlights">精彩片段</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item label="输出格式">
              <el-select v-model="analysisOptions.outputFormat" placeholder="选择输出格式">
                <el-option label="MP4 (推荐)" value="mp4" />
                <el-option label="AVI" value="avi" />
                <el-option label="MOV" value="mov" />
              </el-select>
            </el-form-item>

            <el-form-item label="视频质量">
              <el-slider 
                v-model="analysisOptions.quality" 
                :min="1" 
                :max="10" 
                :marks="qualityMarks"
                show-stops
              />
            </el-form-item>
          </el-form>
        </el-card>
      </div>

      <!-- 操作按钮 -->
      <div class="actions">
        <el-button size="large" @click="$router.go(-1)">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
        <el-button 
          type="primary" 
          size="large" 
          :disabled="!canStartAnalysis"
          :loading="starting"
          @click="startAnalysis"
        >
          <el-icon><VideoPlay /></el-icon>
          开始分析
        </el-button>
      </div>

      <!-- 历史上传 -->
      <div class="history-section" v-if="uploadHistory.length > 0">
        <h3 class="history-title">最近上传</h3>
        <div class="history-list">
          <div 
            class="history-item" 
            v-for="item in uploadHistory" 
            :key="item.id"
            @click="viewAnalysis(item.id)"
          >
            <div class="history-thumbnail">
              <img v-if="item.thumbnail" :src="item.thumbnail" alt="缩略图" />
              <el-icon v-else><VideoPlay /></el-icon>
            </div>
            <div class="history-info">
              <div class="history-name">{{ item.name }}</div>
              <div class="history-time">{{ formatTime(item.uploadTime) }}</div>
            </div>
            <div class="history-status">
              <el-tag :type="getStatusType(item.status)">
                {{ getStatusText(item.status) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, VideoPlay, Document, Delete, Setting, ArrowLeft } from '@element-plus/icons-vue'
import type { UploadInstance, UploadProgressEvent, UploadFile } from 'element-plus'
import { useAppStore } from '@/stores'
import type { VideoInfo } from '@/types'

const router = useRouter()
const appStore = useAppStore()

// 上传相关
const uploadRef = ref<UploadInstance>()
const uploading = ref(false)
const uploadProgress = ref(0)
const currentFile = ref<File | null>(null)
const starting = ref(false)

// 上传配置
const uploadUrl = '/api/upload/video'

// 分析选项
const analysisOptions = ref({
  precision: 'standard',
  features: ['players', 'actions', 'stats', 'highlights'],
  outputFormat: 'mp4',
  quality: 7
})

// 质量标记
const qualityMarks = {
  1: '低',
  5: '中',
  10: '高'
}

// 上传历史
const uploadHistory = ref<VideoInfo[]>([])

// 计算属性
const canStartAnalysis = computed(() => {
  return currentFile.value && !uploading.value
})

// 文件上传前检查
const beforeUpload = (file: File) => {
  const isVideo = file.type.startsWith('video/')
  const isLt2G = file.size / 1024 / 1024 / 1024 < 2

  if (!isVideo) {
    ElMessage.error('只能上传视频文件!')
    return false
  }
  if (!isLt2G) {
    ElMessage.error('视频文件大小不能超过 2GB!')
    return false
  }

  currentFile.value = file
  return true
}

// 上传进度
const onProgress = (event: UploadProgressEvent) => {
  uploading.value = true
  uploadProgress.value = Math.round(event.percent || 0)
}

// 上传成功
const onSuccess = (response: any) => {
  uploading.value = false
  uploadProgress.value = 100
  ElMessage.success('视频上传成功!')
  
  // 保存视频信息
  const videoInfo: VideoInfo = {
    id: response.data.id,
    name: currentFile.value?.name || '',
    url: response.data.url,
    duration: response.data.duration,
    size: currentFile.value?.size || 0,
    uploadTime: new Date().toISOString(),
    thumbnail: response.data.thumbnail
  }
  
  appStore.setCurrentVideo(videoInfo)
}

// 上传失败
const onError = (error: any) => {
  uploading.value = false
  uploadProgress.value = 0
  ElMessage.error('视频上传失败: ' + error.message)
}

// 移除文件
const removeFile = () => {
  currentFile.value = null
  uploadProgress.value = 0
}

// 开始分析
const startAnalysis = async () => {
  if (!currentFile.value) return

  try {
    starting.value = true
    
    // 这里调用分析API
    const response = await fetch('/api/analysis/start', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        videoId: appStore.currentVideo?.id,
        options: analysisOptions.value
      })
    })

    const result = await response.json()
    
    if (result.code === 200) {
      ElMessage.success('分析任务已启动!')
      router.push(`/analysis/${result.data.taskId}`)
    } else {
      throw new Error(result.message)
    }
  } catch (error: any) {
    ElMessage.error('启动分析失败: ' + error.message)
  } finally {
    starting.value = false
  }
}

// 查看分析结果
const viewAnalysis = (videoId: string) => {
  router.push(`/analysis/${videoId}`)
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化时间
const formatTime = (time: string) => {
  return new Date(time).toLocaleString()
}

// 获取状态类型
const getStatusType = (status: string) => {
  const types: Record<string, string> = {
    'pending': 'info',
    'processing': 'warning',
    'completed': 'success',
    'failed': 'danger'
  }
  return types[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const texts: Record<string, string> = {
    'pending': '等待中',
    'processing': '分析中',
    'completed': '已完成',
    'failed': '失败'
  }
  return texts[status] || '未知'
}

// 加载上传历史
const loadUploadHistory = async () => {
  try {
    const response = await fetch('/api/upload/history')
    const result = await response.json()
    if (result.code === 200) {
      uploadHistory.value = result.data
    }
  } catch (error) {
    console.error('加载上传历史失败:', error)
  }
}

onMounted(() => {
  loadUploadHistory()
})
</script>

<style lang="scss" scoped>
.upload-page {
  min-height: 100vh;
  padding: 20px;
}

.container {
  max-width: 1000px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.page-subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
}

.upload-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  margin-bottom: 40px;
}

.upload-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.upload-dragger {
  :deep(.el-upload-dragger) {
    background: transparent;
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 60px 40px;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: rgba(255, 255, 255, 0.6);
      background: rgba(255, 255, 255, 0.05);
    }
  }
}

.upload-content {
  text-align: center;
}

.upload-icon {
  font-size: 4rem;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 20px;
}

.upload-progress {
  margin-bottom: 20px;
}

.upload-title {
  font-size: 1.2rem;
  color: white;
  margin-bottom: 10px;
}

.upload-hint {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

.file-info {
  margin-top: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.file-details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-meta {
  flex: 1;
}

.file-name {
  color: white;
  font-weight: 500;
}

.file-size {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

.options-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-weight: 600;
}

:deep(.el-form-item__label) {
  color: rgba(255, 255, 255, 0.9);
}

:deep(.el-radio__label),
:deep(.el-checkbox__label) {
  color: rgba(255, 255, 255, 0.8);
}

.actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 60px;
}

.history-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.history-title {
  color: white;
  font-size: 1.3rem;
  margin-bottom: 20px;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

.history-thumbnail {
  width: 60px;
  height: 40px;
  border-radius: 6px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.history-info {
  flex: 1;
}

.history-name {
  color: white;
  font-weight: 500;
  margin-bottom: 4px;
}

.history-time {
  color: rgba(255, 255, 255, 0.6);
  font-size: 12px;
}

@media (max-width: 768px) {
  .upload-section {
    grid-template-columns: 1fr;
  }
  
  .page-title {
    font-size: 2rem;
  }
  
  .actions {
    flex-direction: column;
    align-items: center;
  }
}
</style>
