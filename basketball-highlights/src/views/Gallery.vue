<template>
  <div class="gallery-page">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">
          <el-icon><Picture /></el-icon>
          集锦画廊
        </h1>
        <p class="page-subtitle">浏览所有精彩的篮球集锦视频</p>
      </div>

      <!-- 筛选和搜索 -->
      <div class="filter-section">
        <el-card class="filter-card">
          <div class="filter-content">
            <div class="search-box">
              <el-input
                v-model="searchQuery"
                placeholder="搜索球员、球队或关键词..."
                size="large"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </div>
            
            <div class="filter-controls">
              <el-select v-model="filterType" placeholder="类型" clearable>
                <el-option label="全部类型" value="" />
                <el-option label="投篮集锦" value="shot" />
                <el-option label="扣篮集锦" value="dunk" />
                <el-option label="助攻集锦" value="assist" />
                <el-option label="防守集锦" value="defense" />
              </el-select>
              
              <el-select v-model="sortBy" placeholder="排序">
                <el-option label="最新上传" value="latest" />
                <el-option label="最受欢迎" value="popular" />
                <el-option label="观看次数" value="views" />
                <el-option label="时长" value="duration" />
              </el-select>
              
              <el-button type="primary" @click="applyFilters">
                <el-icon><Filter /></el-icon>
                筛选
              </el-button>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 集锦网格 -->
      <div class="gallery-grid">
        <div 
          v-for="video in filteredVideos" 
          :key="video.id"
          class="video-card"
          @click="playVideo(video)"
        >
          <div class="video-thumbnail">
            <img :src="video.thumbnail" :alt="video.title" />
            <div class="video-overlay">
              <el-icon class="play-icon"><VideoPlay /></el-icon>
            </div>
            <div class="video-duration">{{ formatDuration(video.duration) }}</div>
            <div class="video-quality">{{ video.quality }}</div>
          </div>
          
          <div class="video-info">
            <h3 class="video-title">{{ video.title }}</h3>
            <div class="video-meta">
              <div class="player-info">
                <el-avatar :size="24" :src="video.playerAvatar">
                  <el-icon><User /></el-icon>
                </el-avatar>
                <span class="player-name">{{ video.playerName }}</span>
              </div>
              <div class="video-stats">
                <span class="views">
                  <el-icon><View /></el-icon>
                  {{ formatNumber(video.views) }}
                </span>
                <span class="likes">
                  <el-icon><Star /></el-icon>
                  {{ formatNumber(video.likes) }}
                </span>
              </div>
            </div>
            <div class="video-tags">
              <el-tag 
                v-for="tag in video.tags.slice(0, 3)" 
                :key="tag"
                size="small"
                class="tag"
              >
                {{ tag }}
              </el-tag>
            </div>
            <div class="video-date">{{ formatDate(video.createdAt) }}</div>
          </div>
          
          <div class="video-actions">
            <el-button size="small" circle @click.stop="likeVideo(video)">
              <el-icon><Star /></el-icon>
            </el-button>
            <el-button size="small" circle @click.stop="shareVideo(video)">
              <el-icon><Share /></el-icon>
            </el-button>
            <el-button size="small" circle @click.stop="downloadVideo(video)">
              <el-icon><Download /></el-icon>
            </el-button>
          </div>
        </div>
      </div>

      <!-- 加载更多 -->
      <div class="load-more" v-if="hasMore">
        <el-button 
          size="large" 
          :loading="loading"
          @click="loadMore"
        >
          加载更多
        </el-button>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredVideos.length === 0" class="empty-state">
        <el-empty description="暂无集锦视频">
          <el-button type="primary" @click="$router.push('/upload')">
            <el-icon><Upload /></el-icon>
            上传第一个视频
          </el-button>
        </el-empty>
      </div>
    </div>

    <!-- 视频播放器对话框 -->
    <el-dialog 
      v-model="playerVisible" 
      :title="currentVideo?.title"
      width="90%"
      :before-close="closePlayer"
      class="video-dialog"
    >
      <div class="video-player-container">
        <video 
          ref="videoPlayer"
          controls
          width="100%"
          height="500"
          :poster="currentVideo?.thumbnail"
          @play="onVideoPlay"
          @pause="onVideoPause"
        >
          <source :src="currentVideo?.url" type="video/mp4">
          您的浏览器不支持视频播放
        </video>
        
        <div class="video-details">
          <div class="video-header">
            <h2>{{ currentVideo?.title }}</h2>
            <div class="video-actions-large">
              <el-button @click="likeVideo(currentVideo)">
                <el-icon><Star /></el-icon>
                点赞 ({{ currentVideo?.likes }})
              </el-button>
              <el-button @click="shareVideo(currentVideo)">
                <el-icon><Share /></el-icon>
                分享
              </el-button>
              <el-button @click="downloadVideo(currentVideo)">
                <el-icon><Download /></el-icon>
                下载
              </el-button>
            </div>
          </div>
          
          <div class="video-description">
            <p>{{ currentVideo?.description }}</p>
          </div>
          
          <div class="video-metadata">
            <div class="meta-item">
              <span class="label">球员:</span>
              <span class="value">{{ currentVideo?.playerName }}</span>
            </div>
            <div class="meta-item">
              <span class="label">时长:</span>
              <span class="value">{{ formatDuration(currentVideo?.duration || 0) }}</span>
            </div>
            <div class="meta-item">
              <span class="label">观看:</span>
              <span class="value">{{ formatNumber(currentVideo?.views || 0) }}</span>
            </div>
            <div class="meta-item">
              <span class="label">上传:</span>
              <span class="value">{{ formatDate(currentVideo?.createdAt || '') }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Picture, Search, Filter, VideoPlay, User, View, Star, 
  Share, Download, Upload 
} from '@element-plus/icons-vue'

// 视频数据接口
interface GalleryVideo {
  id: string
  title: string
  description: string
  thumbnail: string
  url: string
  duration: number
  quality: string
  playerName: string
  playerAvatar: string
  views: number
  likes: number
  tags: string[]
  createdAt: string
  type: string
}

// 响应式数据
const searchQuery = ref('')
const filterType = ref('')
const sortBy = ref('latest')
const loading = ref(false)
const hasMore = ref(true)
const playerVisible = ref(false)
const currentVideo = ref<GalleryVideo | null>(null)
const videoPlayer = ref<HTMLVideoElement>()

// 模拟视频数据
const videos = ref<GalleryVideo[]>([
  {
    id: '1',
    title: '勒布朗·詹姆斯 - 2024赛季精彩集锦',
    description: '包含本赛季最精彩的扣篮、助攻和关键球',
    thumbnail: 'https://via.placeholder.com/400x225/667eea/ffffff?text=LeBron+Highlights',
    url: '/videos/lebron-highlights.mp4',
    duration: 180,
    quality: 'HD',
    playerName: '勒布朗·詹姆斯',
    playerAvatar: '',
    views: 15420,
    likes: 892,
    tags: ['扣篮', '助攻', '关键球', '湖人'],
    createdAt: '2024-01-15T10:30:00Z',
    type: 'mixed'
  },
  {
    id: '2',
    title: '斯蒂芬·库里 - 三分球集锦',
    description: '超远距离三分球和关键时刻投篮',
    thumbnail: 'https://via.placeholder.com/400x225/f093fb/ffffff?text=Curry+3PT',
    url: '/videos/curry-threes.mp4',
    duration: 120,
    quality: '4K',
    playerName: '斯蒂芬·库里',
    playerAvatar: '',
    views: 23150,
    likes: 1456,
    tags: ['三分球', '投篮', '勇士'],
    createdAt: '2024-01-14T15:45:00Z',
    type: 'shot'
  },
  {
    id: '3',
    title: '扬尼斯·阿德托昆博 - 扣篮合集',
    description: '希腊怪兽的暴力扣篮瞬间',
    thumbnail: 'https://via.placeholder.com/400x225/43e97b/ffffff?text=Giannis+Dunks',
    url: '/videos/giannis-dunks.mp4',
    duration: 95,
    quality: 'HD',
    playerName: '扬尼斯·阿德托昆博',
    playerAvatar: '',
    views: 18750,
    likes: 1123,
    tags: ['扣篮', '暴力', '雄鹿'],
    createdAt: '2024-01-13T09:20:00Z',
    type: 'dunk'
  },
  {
    id: '4',
    title: '克里斯·保罗 - 助攻大师',
    description: '精妙的传球和组织进攻',
    thumbnail: 'https://via.placeholder.com/400x225/4facfe/ffffff?text=CP3+Assists',
    url: '/videos/cp3-assists.mp4',
    duration: 150,
    quality: 'HD',
    playerName: '克里斯·保罗',
    playerAvatar: '',
    views: 12340,
    likes: 678,
    tags: ['助攻', '传球', '组织', '太阳'],
    createdAt: '2024-01-12T14:10:00Z',
    type: 'assist'
  }
])

// 计算属性
const filteredVideos = computed(() => {
  let result = [...videos.value]
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(video => 
      video.title.toLowerCase().includes(query) ||
      video.playerName.toLowerCase().includes(query) ||
      video.tags.some(tag => tag.toLowerCase().includes(query))
    )
  }
  
  // 类型过滤
  if (filterType.value) {
    result = result.filter(video => video.type === filterType.value)
  }
  
  // 排序
  switch (sortBy.value) {
    case 'latest':
      result.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      break
    case 'popular':
      result.sort((a, b) => b.likes - a.likes)
      break
    case 'views':
      result.sort((a, b) => b.views - a.views)
      break
    case 'duration':
      result.sort((a, b) => b.duration - a.duration)
      break
  }
  
  return result
})

// 方法
const applyFilters = () => {
  ElMessage.success('筛选已应用')
}

const playVideo = (video: GalleryVideo) => {
  currentVideo.value = video
  playerVisible.value = true
  
  // 增加观看次数
  video.views++
}

const closePlayer = () => {
  if (videoPlayer.value) {
    videoPlayer.value.pause()
  }
  playerVisible.value = false
  currentVideo.value = null
}

const onVideoPlay = () => {
  // 视频开始播放
}

const onVideoPause = () => {
  // 视频暂停
}

const likeVideo = (video: GalleryVideo | null) => {
  if (video) {
    video.likes++
    ElMessage.success('已点赞!')
  }
}

const shareVideo = (video: GalleryVideo | null) => {
  if (video) {
    // 复制分享链接到剪贴板
    navigator.clipboard.writeText(`${window.location.origin}/video/${video.id}`)
    ElMessage.success('分享链接已复制到剪贴板!')
  }
}

const downloadVideo = (video: GalleryVideo | null) => {
  if (video) {
    ElMessage.success('开始下载视频...')
    // 这里实现下载逻辑
  }
}

const loadMore = () => {
  loading.value = true
  
  // 模拟加载更多数据
  setTimeout(() => {
    loading.value = false
    hasMore.value = false
    ElMessage.info('没有更多视频了')
  }, 1000)
}

const formatDuration = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - date.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays === 1) {
    return '1天前'
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else if (diffDays < 30) {
    return `${Math.floor(diffDays / 7)}周前`
  } else {
    return date.toLocaleDateString()
  }
}

onMounted(() => {
  // 页面加载时的初始化
})
</script>

<style lang="scss" scoped>
.gallery-page {
  min-height: 100vh;
  padding: 20px;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.page-subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
}

// 筛选区域
.filter-section {
  margin-bottom: 30px;
}

.filter-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.filter-content {
  display: flex;
  gap: 20px;
  align-items: center;
}

.search-box {
  flex: 1;
  max-width: 400px;
}

.filter-controls {
  display: flex;
  gap: 12px;
}

// 视频网格
.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.video-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  
  &:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 32px rgba(0, 0, 0, 0.2);
    
    .video-overlay {
      opacity: 1;
    }
    
    .video-actions {
      opacity: 1;
    }
  }
}

.video-thumbnail {
  position: relative;
  height: 200px;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.play-icon {
  font-size: 48px;
  color: white;
}

.video-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.video-quality {
  position: absolute;
  top: 8px;
  left: 8px;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
}

.video-info {
  padding: 16px;
}

.video-title {
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 12px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.player-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.player-name {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  font-weight: 500;
}

.video-stats {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.views, .likes {
  display: flex;
  align-items: center;
  gap: 4px;
}

.video-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  margin-bottom: 8px;
}

.tag {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
}

.video-date {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.video-actions {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  gap: 6px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

// 加载更多
.load-more {
  text-align: center;
  margin-bottom: 40px;
}

// 空状态
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

// 视频播放器对话框
.video-dialog {
  :deep(.el-dialog__body) {
    padding: 0;
  }
}

.video-player-container {
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.video-details {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 20px;
  color: white;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.video-actions-large {
  display: flex;
  gap: 12px;
}

.video-description {
  margin-bottom: 20px;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.9);
}

.video-metadata {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.meta-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  font-weight: 600;
}

.value {
  font-size: 14px;
  color: white;
}

// 响应式设计
@media (max-width: 768px) {
  .filter-content {
    flex-direction: column;
    gap: 16px;
  }
  
  .filter-controls {
    width: 100%;
    justify-content: space-between;
  }
  
  .gallery-grid {
    grid-template-columns: 1fr;
  }
  
  .video-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .video-actions-large {
    justify-content: center;
  }
  
  .video-metadata {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .video-metadata {
    grid-template-columns: 1fr;
  }
}
</style>
