<template>
  <div class="home">
    <!-- 英雄区域 -->
    <section class="hero-section">
      <div class="hero-content">
        <div class="hero-text">
          <h1 class="hero-title">
            <el-icon class="hero-icon"><Basketball /></el-icon>
            AI篮球视频集锦生成器
          </h1>
          <p class="hero-subtitle">
            上传完整比赛视频，AI智能识别每位球员的精彩瞬间，
            <br>自动生成个人专属集锦视频
          </p>
          <div class="hero-actions">
            <el-button 
              type="primary" 
              size="large" 
              @click="$router.push('/upload')"
              class="upload-btn"
            >
              <el-icon><Upload /></el-icon>
              开始上传视频
            </el-button>
            <el-button 
              size="large" 
              @click="$router.push('/gallery')"
              class="gallery-btn"
            >
              <el-icon><Picture /></el-icon>
              浏览集锦画廊
            </el-button>
          </div>
        </div>
        <div class="hero-visual">
          <div class="basketball-court">
            <div class="court-line"></div>
            <div class="center-circle"></div>
            <div class="player-dot" v-for="i in 10" :key="i" :style="getPlayerStyle(i)"></div>
          </div>
        </div>
      </div>
    </section>

    <!-- 功能特色 -->
    <section class="features-section">
      <div class="container">
        <h2 class="section-title">核心功能</h2>
        <div class="features-grid">
          <div class="feature-card" v-for="feature in features" :key="feature.id">
            <div class="feature-icon" :style="{ background: feature.color }">
              <component :is="feature.icon" />
            </div>
            <h3 class="feature-title">{{ feature.title }}</h3>
            <p class="feature-description">{{ feature.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 工作流程 -->
    <section class="workflow-section">
      <div class="container">
        <h2 class="section-title">工作流程</h2>
        <div class="workflow-steps">
          <div class="step" v-for="(step, index) in workflowSteps" :key="step.id">
            <div class="step-number">{{ index + 1 }}</div>
            <div class="step-content">
              <h3 class="step-title">{{ step.title }}</h3>
              <p class="step-description">{{ step.description }}</p>
            </div>
            <div class="step-arrow" v-if="index < workflowSteps.length - 1">
              <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 统计数据 -->
    <section class="stats-section">
      <div class="container">
        <div class="stats-grid">
          <div class="stat-item" v-for="stat in stats" :key="stat.label">
            <div class="stat-number">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Basketball, Upload, Picture, ArrowRight, VideoPlay, User, TrendCharts, Setting } from '@element-plus/icons-vue'

// 功能特色
const features = ref([
  {
    id: 1,
    icon: VideoPlay,
    title: 'AI视频分析',
    description: '先进的计算机视觉技术，自动识别球员动作和精彩瞬间',
    color: 'linear-gradient(45deg, #667eea, #764ba2)'
  },
  {
    id: 2,
    icon: User,
    title: '球员识别',
    description: '智能识别每位球员，追踪个人表现和统计数据',
    color: 'linear-gradient(45deg, #f093fb, #f5576c)'
  },
  {
    id: 3,
    icon: TrendCharts,
    title: '数据统计',
    description: '详细的个人和团队数据分析，包括得分、助攻、篮板等',
    color: 'linear-gradient(45deg, #4facfe, #00f2fe)'
  },
  {
    id: 4,
    icon: Setting,
    title: '自定义剪辑',
    description: '根据需求自定义集锦内容，支持多种导出格式',
    color: 'linear-gradient(45deg, #43e97b, #38f9d7)'
  }
])

// 工作流程
const workflowSteps = ref([
  {
    id: 1,
    title: '上传视频',
    description: '上传完整的篮球比赛录像'
  },
  {
    id: 2,
    title: 'AI分析',
    description: '智能识别球员和精彩动作'
  },
  {
    id: 3,
    title: '生成集锦',
    description: '自动剪辑个人精彩片段'
  },
  {
    id: 4,
    title: '下载分享',
    description: '导出高质量集锦视频'
  }
])

// 统计数据
const stats = ref([
  { value: '1000+', label: '处理视频数' },
  { value: '50000+', label: '识别球员数' },
  { value: '98%', label: '识别准确率' },
  { value: '5分钟', label: '平均处理时间' }
])

// 球员位置样式
const getPlayerStyle = (index: number) => {
  const positions = [
    { left: '20%', top: '30%' },
    { left: '80%', top: '30%' },
    { left: '50%', top: '20%' },
    { left: '30%', top: '50%' },
    { left: '70%', top: '50%' },
    { left: '20%', top: '70%' },
    { left: '80%', top: '70%' },
    { left: '50%', top: '80%' },
    { left: '40%', top: '40%' },
    { left: '60%', top: '60%' }
  ]
  
  const pos = positions[index - 1] || { left: '50%', top: '50%' }
  return {
    left: pos.left,
    top: pos.top,
    animationDelay: `${index * 0.2}s`
  }
}

onMounted(() => {
  // 页面加载动画
})
</script>

<style lang="scss" scoped>
.home {
  min-height: 100vh;
}

// 英雄区域
.hero-section {
  padding: 80px 20px;
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  margin: 20px;
  border-radius: 20px;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
}

.hero-text {
  text-align: left;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.hero-icon {
  font-size: 4rem;
  color: #fbbf24;
}

.hero-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 40px;
  line-height: 1.6;
}

.hero-actions {
  display: flex;
  gap: 20px;
}

.upload-btn, .gallery-btn {
  padding: 16px 32px;
  font-size: 16px;
  border-radius: 12px;
  font-weight: 600;
}

.upload-btn {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border: none;
}

.gallery-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

// 篮球场可视化
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;
}

.basketball-court {
  width: 300px;
  height: 200px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 10px;
  position: relative;
  overflow: hidden;
}

.court-line {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-50%);
}

.center-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.player-dot {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #fbbf24;
  border-radius: 50%;
  animation: playerMove 3s ease-in-out infinite;
}

@keyframes playerMove {
  0%, 100% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.2); opacity: 1; }
}

// 功能特色
.features-section {
  padding: 80px 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  text-align: center;
  margin-bottom: 60px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
}

.feature-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px 30px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.feature-icon {
  width: 80px;
  height: 80px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  font-size: 32px;
  color: white;
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  margin-bottom: 15px;
}

.feature-description {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

// 工作流程
.workflow-section {
  padding: 80px 20px;
  background: rgba(255, 255, 255, 0.05);
}

.workflow-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1000px;
  margin: 0 auto;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  flex: 1;
  position: relative;
}

.step-number {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
  color: white;
  margin-bottom: 20px;
}

.step-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  margin-bottom: 10px;
}

.step-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.step-arrow {
  position: absolute;
  right: -30px;
  top: 30px;
  font-size: 24px;
  color: rgba(255, 255, 255, 0.5);
}

// 统计数据
.stats-section {
  padding: 60px 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  max-width: 800px;
  margin: 0 auto;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: 700;
  color: #fbbf24;
  margin-bottom: 10px;
}

.stat-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
}

// 响应式设计
@media (max-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .hero-text {
    text-align: center;
  }

  .hero-title {
    font-size: 2.5rem;
    justify-content: center;
  }

  .hero-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .workflow-steps {
    flex-direction: column;
    gap: 40px;
  }

  .step-arrow {
    display: none;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
