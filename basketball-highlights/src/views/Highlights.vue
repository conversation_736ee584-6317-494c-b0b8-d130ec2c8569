<template>
  <div class="highlights-page">
    <div class="container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h1 class="page-title">
          <el-icon><Star /></el-icon>
          个人集锦
        </h1>
        <p class="page-subtitle">查看和管理生成的个人精彩集锦</p>
      </div>

      <!-- 球员选择 -->
      <div class="player-selector">
        <el-card class="selector-card">
          <template #header>
            <div class="card-header">
              <el-icon><User /></el-icon>
              <span>选择球员</span>
            </div>
          </template>
          
          <div class="player-grid">
            <div 
              v-for="player in players" 
              :key="player.id"
              class="player-card"
              :class="{ active: selectedPlayer?.id === player.id }"
              @click="selectPlayer(player)"
            >
              <div class="player-avatar">
                <img v-if="player.avatar" :src="player.avatar" :alt="player.name" />
                <el-icon v-else><User /></el-icon>
              </div>
              <div class="player-info">
                <div class="player-name">{{ player.name || '球员' + player.number }}</div>
                <div class="player-stats">
                  <span>{{ player.highlights.length }}个片段</span>
                  <span>{{ formatTime(player.stats.totalTime) }}</span>
                </div>
              </div>
              <div class="player-badge" v-if="player.highlights.length > 0">
                {{ player.highlights.length }}
              </div>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 集锦内容 -->
      <div v-if="selectedPlayer" class="highlights-content">
        <!-- 球员详情 -->
        <div class="player-details">
          <el-card class="details-card">
            <div class="player-header">
              <div class="player-avatar-large">
                <img v-if="selectedPlayer.avatar" :src="selectedPlayer.avatar" :alt="selectedPlayer.name" />
                <el-icon v-else><User /></el-icon>
              </div>
              <div class="player-meta">
                <h2 class="player-name">{{ selectedPlayer.name || '球员' + selectedPlayer.number }}</h2>
                <div class="player-tags">
                  <el-tag v-if="selectedPlayer.number" type="primary">#{{ selectedPlayer.number }}</el-tag>
                  <el-tag v-if="selectedPlayer.team">{{ selectedPlayer.team }}</el-tag>
                  <el-tag v-if="selectedPlayer.position">{{ selectedPlayer.position }}</el-tag>
                </div>
              </div>
              <div class="player-actions">
                <el-button type="primary" @click="generateHighlight">
                  <el-icon><VideoPlay /></el-icon>
                  生成集锦
                </el-button>
                <el-button @click="downloadHighlight">
                  <el-icon><Download /></el-icon>
                  下载
                </el-button>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 统计数据 -->
        <div class="stats-section">
          <el-row :gutter="20">
            <el-col :span="6" v-for="stat in playerStats" :key="stat.key">
              <el-card class="stat-card">
                <div class="stat-content">
                  <div class="stat-icon" :style="{ background: stat.color }">
                    <component :is="stat.icon" />
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">{{ stat.value }}</div>
                    <div class="stat-label">{{ stat.label }}</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>

        <!-- 精彩片段列表 -->
        <div class="highlights-list">
          <el-card class="list-card">
            <template #header>
              <div class="card-header">
                <el-icon><Collection /></el-icon>
                <span>精彩片段 ({{ selectedPlayer.highlights.length }})</span>
                <div class="header-actions">
                  <el-select v-model="sortBy" placeholder="排序方式" size="small">
                    <el-option label="时间顺序" value="time" />
                    <el-option label="置信度" value="confidence" />
                    <el-option label="类型" value="type" />
                  </el-select>
                  <el-select v-model="filterType" placeholder="筛选类型" size="small">
                    <el-option label="全部" value="" />
                    <el-option label="投篮" value="shot" />
                    <el-option label="扣篮" value="dunk" />
                    <el-option label="三分" value="three_pointer" />
                    <el-option label="助攻" value="assist" />
                  </el-select>
                </div>
              </div>
            </template>

            <div class="highlights-grid">
              <div 
                v-for="highlight in filteredHighlights" 
                :key="highlight.id"
                class="highlight-item"
                @click="playHighlight(highlight)"
              >
                <div class="highlight-thumbnail">
                  <img v-if="highlight.thumbnail" :src="highlight.thumbnail" alt="缩略图" />
                  <div v-else class="thumbnail-placeholder">
                    <el-icon><VideoPlay /></el-icon>
                  </div>
                  <div class="highlight-duration">
                    {{ formatDuration(highlight.endTime - highlight.startTime) }}
                  </div>
                  <div class="highlight-type">
                    {{ getTypeLabel(highlight.type) }}
                  </div>
                </div>
                <div class="highlight-info">
                  <div class="highlight-title">{{ highlight.description }}</div>
                  <div class="highlight-meta">
                    <span class="highlight-time">{{ formatTime(highlight.startTime) }}</span>
                    <span class="highlight-confidence">置信度: {{ highlight.confidence }}%</span>
                  </div>
                  <div class="highlight-tags">
                    <el-tag 
                      v-for="tag in highlight.tags" 
                      :key="tag"
                      size="small"
                      class="tag"
                    >
                      {{ tag }}
                    </el-tag>
                  </div>
                </div>
                <div class="highlight-actions">
                  <el-button size="small" circle @click.stop="editHighlight(highlight)">
                    <el-icon><Edit /></el-icon>
                  </el-button>
                  <el-button size="small" circle type="danger" @click.stop="deleteHighlight(highlight)">
                    <el-icon><Delete /></el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <el-empty description="请选择一个球员查看集锦">
          <el-button type="primary" @click="$router.push('/upload')">
            <el-icon><Upload /></el-icon>
            上传新视频
          </el-button>
        </el-empty>
      </div>
    </div>

    <!-- 视频播放器对话框 -->
    <el-dialog 
      v-model="playerVisible" 
      title="精彩片段播放" 
      width="80%"
      :before-close="closePlayer"
    >
      <div class="video-player-container">
        <video 
          ref="videoPlayer"
          controls
          width="100%"
          height="400"
          @loadedmetadata="onVideoLoaded"
        >
          <source :src="currentVideoUrl" type="video/mp4">
          您的浏览器不支持视频播放
        </video>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Star, User, VideoPlay, Download, Collection, Edit, Delete, Upload
} from '@element-plus/icons-vue'
import { useAppStore } from '@/stores'
import type { PlayerInfo, HighlightClip, HighlightType } from '@/types'

const route = useRoute()
const appStore = useAppStore()

// 响应式数据
const players = ref<PlayerInfo[]>([])
const selectedPlayer = ref<PlayerInfo | null>(null)
const sortBy = ref('time')
const filterType = ref('')
const playerVisible = ref(false)
const currentVideoUrl = ref('')
const videoPlayer = ref<HTMLVideoElement>()

// 模拟数据
const mockPlayers: PlayerInfo[] = [
  {
    id: '1',
    name: '勒布朗·詹姆斯',
    number: '23',
    team: '湖人队',
    position: '前锋',
    avatar: '',
    stats: {
      totalTime: 2400,
      shots: 15,
      makes: 9,
      assists: 8,
      rebounds: 12,
      steals: 3,
      blocks: 2,
      turnovers: 4,
      points: 28
    },
    highlights: [
      {
        id: '1',
        playerId: '1',
        type: 'dunk' as HighlightType,
        startTime: 120,
        endTime: 125,
        description: '暴力扣篮',
        confidence: 95,
        thumbnail: '',
        tags: ['扣篮', '得分']
      },
      {
        id: '2',
        playerId: '1',
        type: 'assist' as HighlightType,
        startTime: 300,
        endTime: 308,
        description: '精妙助攻',
        confidence: 88,
        thumbnail: '',
        tags: ['助攻', '传球']
      }
    ]
  },
  {
    id: '2',
    name: '斯蒂芬·库里',
    number: '30',
    team: '勇士队',
    position: '后卫',
    avatar: '',
    stats: {
      totalTime: 2200,
      shots: 20,
      makes: 12,
      assists: 6,
      rebounds: 5,
      steals: 2,
      blocks: 0,
      turnovers: 3,
      points: 35
    },
    highlights: [
      {
        id: '3',
        playerId: '2',
        type: 'three_pointer' as HighlightType,
        startTime: 180,
        endTime: 185,
        description: '超远三分',
        confidence: 92,
        thumbnail: '',
        tags: ['三分', '得分']
      }
    ]
  }
]

// 计算属性
const playerStats = computed(() => {
  if (!selectedPlayer.value) return []
  
  const stats = selectedPlayer.value.stats
  return [
    {
      key: 'points',
      label: '得分',
      value: stats.points,
      icon: 'Trophy',
      color: 'linear-gradient(45deg, #f093fb, #f5576c)'
    },
    {
      key: 'assists',
      label: '助攻',
      value: stats.assists,
      icon: 'Share',
      color: 'linear-gradient(45deg, #4facfe, #00f2fe)'
    },
    {
      key: 'rebounds',
      label: '篮板',
      value: stats.rebounds,
      icon: 'Basketball',
      color: 'linear-gradient(45deg, #43e97b, #38f9d7)'
    },
    {
      key: 'efficiency',
      label: '命中率',
      value: `${Math.round((stats.makes / stats.shots) * 100)}%`,
      icon: 'TrendCharts',
      color: 'linear-gradient(45deg, #667eea, #764ba2)'
    }
  ]
})

const filteredHighlights = computed(() => {
  if (!selectedPlayer.value) return []
  
  let highlights = [...selectedPlayer.value.highlights]
  
  // 筛选类型
  if (filterType.value) {
    highlights = highlights.filter(h => h.type === filterType.value)
  }
  
  // 排序
  switch (sortBy.value) {
    case 'time':
      highlights.sort((a, b) => a.startTime - b.startTime)
      break
    case 'confidence':
      highlights.sort((a, b) => b.confidence - a.confidence)
      break
    case 'type':
      highlights.sort((a, b) => a.type.localeCompare(b.type))
      break
  }
  
  return highlights
})

// 方法
const selectPlayer = (player: PlayerInfo) => {
  selectedPlayer.value = player
}

const generateHighlight = () => {
  ElMessage.success('正在生成集锦视频...')
}

const downloadHighlight = () => {
  ElMessage.success('开始下载集锦视频...')
}

const playHighlight = (highlight: HighlightClip) => {
  currentVideoUrl.value = '/api/video/highlight/' + highlight.id
  playerVisible.value = true
}

const closePlayer = () => {
  if (videoPlayer.value) {
    videoPlayer.value.pause()
  }
  playerVisible.value = false
}

const onVideoLoaded = () => {
  // 视频加载完成后的处理
}

const editHighlight = (highlight: HighlightClip) => {
  ElMessage.info('编辑功能开发中...')
}

const deleteHighlight = async (highlight: HighlightClip) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个精彩片段吗？',
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('片段已删除')
  } catch {
    // 用户取消
  }
}

const getTypeLabel = (type: HighlightType) => {
  const labels: Record<HighlightType, string> = {
    shot: '投篮',
    dunk: '扣篮',
    three_pointer: '三分',
    assist: '助攻',
    steal: '抢断',
    block: '盖帽',
    rebound: '篮板',
    defense: '防守',
    fast_break: '快攻'
  }
  return labels[type] || type
}

const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const formatDuration = (seconds: number) => {
  return `${seconds}s`
}

onMounted(() => {
  // 加载球员数据
  players.value = mockPlayers
  
  // 如果有默认选择的球员
  if (players.value.length > 0) {
    selectedPlayer.value = players.value[0]
  }
})
</script>

<style lang="scss" scoped>
.highlights-page {
  min-height: 100vh;
  padding: 20px;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.page-subtitle {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.8);
}

// 球员选择器
.player-selector {
  margin-bottom: 30px;
}

.selector-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-weight: 600;
}

.player-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.player-card {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }
  
  &.active {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
  }
}

.player-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.player-info {
  flex: 1;
}

.player-name {
  color: white;
  font-weight: 600;
  margin-bottom: 4px;
}

.player-stats {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.player-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #f56c6c;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

// 球员详情
.player-details {
  margin-bottom: 30px;
}

.details-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.player-header {
  display: flex;
  align-items: center;
  gap: 20px;
}

.player-avatar-large {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.player-meta {
  flex: 1;
}

.player-name {
  color: white;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 8px;
}

.player-tags {
  display: flex;
  gap: 8px;
}

.player-actions {
  display: flex;
  gap: 12px;
}

// 统计数据
.stats-section {
  margin-bottom: 30px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
}

.stat-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

// 精彩片段列表
.highlights-list {
  margin-bottom: 30px;
}

.list-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-actions {
  display: flex;
  gap: 12px;
  margin-left: auto;
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.highlight-item {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-4px);
  }
}

.highlight-thumbnail {
  position: relative;
  height: 160px;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.thumbnail-placeholder {
  font-size: 48px;
  color: rgba(255, 255, 255, 0.5);
}

.highlight-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.highlight-type {
  position: absolute;
  top: 8px;
  left: 8px;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.highlight-info {
  padding: 16px;
}

.highlight-title {
  color: white;
  font-weight: 600;
  margin-bottom: 8px;
}

.highlight-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.highlight-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.tag {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
}

.highlight-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.highlight-item:hover .highlight-actions {
  opacity: 1;
}

// 空状态
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

// 视频播放器
.video-player-container {
  text-align: center;
}

// 响应式设计
@media (max-width: 768px) {
  .player-header {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .player-actions {
    justify-content: center;
  }
  
  .highlights-grid {
    grid-template-columns: 1fr;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
