<template>
  <div class="player-page">
    <div class="container">
      <!-- 球员头部信息 -->
      <div class="player-header">
        <el-card class="header-card">
          <div class="player-profile">
            <div class="player-avatar-section">
              <div class="player-avatar-large">
                <img v-if="player?.avatar" :src="player.avatar" :alt="player.name" />
                <el-icon v-else><User /></el-icon>
              </div>
              <div class="player-status">
                <el-tag type="success">活跃</el-tag>
              </div>
            </div>
            
            <div class="player-info">
              <h1 class="player-name">{{ player?.name || '未知球员' }}</h1>
              <div class="player-meta">
                <div class="meta-item" v-if="player?.number">
                  <span class="label">球衣号码:</span>
                  <span class="value">#{{ player.number }}</span>
                </div>
                <div class="meta-item" v-if="player?.team">
                  <span class="label">所属球队:</span>
                  <span class="value">{{ player.team }}</span>
                </div>
                <div class="meta-item" v-if="player?.position">
                  <span class="label">场上位置:</span>
                  <span class="value">{{ player.position }}</span>
                </div>
              </div>
              <div class="player-summary">
                <p>这位球员在本赛季表现出色，展现了优秀的技术水平和比赛态度。</p>
              </div>
            </div>
            
            <div class="player-actions">
              <el-button type="primary" size="large" @click="generateHighlights">
                <el-icon><VideoPlay /></el-icon>
                生成集锦
              </el-button>
              <el-button size="large" @click="downloadData">
                <el-icon><Download /></el-icon>
                下载数据
              </el-button>
              <el-button size="large" @click="sharePlayer">
                <el-icon><Share /></el-icon>
                分享
              </el-button>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 统计数据概览 -->
      <div class="stats-overview">
        <el-row :gutter="20">
          <el-col :span="6" v-for="stat in overviewStats" :key="stat.key">
            <el-card class="stat-card">
              <div class="stat-content">
                <div class="stat-icon" :style="{ background: stat.color }">
                  <component :is="stat.icon" />
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ stat.value }}</div>
                  <div class="stat-label">{{ stat.label }}</div>
                  <div class="stat-change" :class="stat.trend">
                    <el-icon>
                      <component :is="stat.trend === 'up' ? 'TrendCharts' : 'Bottom'" />
                    </el-icon>
                    {{ stat.change }}
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 详细统计和图表 -->
      <div class="detailed-stats">
        <el-row :gutter="20">
          <!-- 技术统计 -->
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <div class="card-header">
                  <el-icon><TrendCharts /></el-icon>
                  <span>技术统计</span>
                </div>
              </template>
              <div class="stats-chart">
                <div class="stat-item" v-for="stat in technicalStats" :key="stat.label">
                  <div class="stat-row">
                    <span class="stat-name">{{ stat.label }}</span>
                    <span class="stat-number">{{ stat.value }}</span>
                  </div>
                  <el-progress 
                    :percentage="stat.percentage" 
                    :color="stat.color"
                    :show-text="false"
                    :stroke-width="8"
                  />
                </div>
              </div>
            </el-card>
          </el-col>

          <!-- 表现趋势 -->
          <el-col :span="12">
            <el-card class="chart-card">
              <template #header>
                <div class="card-header">
                  <el-icon><DataLine /></el-icon>
                  <span>表现趋势</span>
                </div>
              </template>
              <div class="trend-chart">
                <div class="chart-placeholder">
                  <el-icon><TrendCharts /></el-icon>
                  <p>图表数据加载中...</p>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 精彩片段 -->
      <div class="highlights-section">
        <el-card class="highlights-card">
          <template #header>
            <div class="card-header">
              <el-icon><Star /></el-icon>
              <span>精彩片段 ({{ player?.highlights?.length || 0 }})</span>
              <div class="header-actions">
                <el-select v-model="highlightFilter" placeholder="筛选类型" size="small">
                  <el-option label="全部" value="" />
                  <el-option label="得分" value="score" />
                  <el-option label="助攻" value="assist" />
                  <el-option label="防守" value="defense" />
                </el-select>
              </div>
            </div>
          </template>

          <div class="highlights-grid">
            <div 
              v-for="highlight in filteredHighlights" 
              :key="highlight.id"
              class="highlight-card"
              @click="playHighlight(highlight)"
            >
              <div class="highlight-thumbnail">
                <img v-if="highlight.thumbnail" :src="highlight.thumbnail" alt="缩略图" />
                <div v-else class="thumbnail-placeholder">
                  <el-icon><VideoPlay /></el-icon>
                </div>
                <div class="highlight-duration">
                  {{ formatDuration(highlight.endTime - highlight.startTime) }}
                </div>
              </div>
              <div class="highlight-info">
                <div class="highlight-title">{{ highlight.description }}</div>
                <div class="highlight-meta">
                  <span class="highlight-type">{{ getTypeLabel(highlight.type) }}</span>
                  <span class="highlight-confidence">{{ highlight.confidence }}%</span>
                </div>
              </div>
            </div>
          </div>

          <div v-if="filteredHighlights.length === 0" class="no-highlights">
            <el-empty description="暂无精彩片段">
              <el-button type="primary" @click="generateHighlights">
                <el-icon><VideoPlay /></el-icon>
                生成集锦
              </el-button>
            </el-empty>
          </div>
        </el-card>
      </div>

      <!-- 比赛历史 -->
      <div class="game-history">
        <el-card class="history-card">
          <template #header>
            <div class="card-header">
              <el-icon><Calendar /></el-icon>
              <span>比赛历史</span>
            </div>
          </template>

          <el-table :data="gameHistory" style="width: 100%">
            <el-table-column prop="date" label="日期" width="120" />
            <el-table-column prop="opponent" label="对手" width="150" />
            <el-table-column prop="result" label="结果" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.result === '胜' ? 'success' : 'danger'">
                  {{ scope.row.result }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="points" label="得分" width="80" />
            <el-table-column prop="assists" label="助攻" width="80" />
            <el-table-column prop="rebounds" label="篮板" width="80" />
            <el-table-column prop="highlights" label="精彩片段" width="100" />
            <el-table-column label="操作">
              <template #default="scope">
                <el-button size="small" @click="viewGameDetails(scope.row)">
                  查看详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  User, VideoPlay, Download, Share, TrendCharts, DataLine, 
  Star, Calendar, Trophy, Basketball 
} from '@element-plus/icons-vue'
import type { PlayerInfo, HighlightClip, HighlightType } from '@/types'

const route = useRoute()

// 响应式数据
const player = ref<PlayerInfo | null>(null)
const highlightFilter = ref('')

// 模拟球员数据
const mockPlayer: PlayerInfo = {
  id: route.params.id as string,
  name: '勒布朗·詹姆斯',
  number: '23',
  team: '洛杉矶湖人',
  position: '小前锋',
  avatar: '',
  stats: {
    totalTime: 2400,
    shots: 18,
    makes: 12,
    assists: 8,
    rebounds: 10,
    steals: 2,
    blocks: 1,
    turnovers: 3,
    points: 28
  },
  highlights: [
    {
      id: '1',
      playerId: route.params.id as string,
      type: 'dunk' as HighlightType,
      startTime: 120,
      endTime: 125,
      description: '暴力扣篮得分',
      confidence: 95,
      thumbnail: '',
      tags: ['扣篮', '得分']
    },
    {
      id: '2',
      playerId: route.params.id as string,
      type: 'assist' as HighlightType,
      startTime: 300,
      endTime: 308,
      description: '精妙助攻',
      confidence: 88,
      thumbnail: '',
      tags: ['助攻', '传球']
    }
  ]
}

// 概览统计
const overviewStats = computed(() => [
  {
    key: 'points',
    label: '场均得分',
    value: player.value?.stats.points || 0,
    change: '+2.3',
    trend: 'up',
    color: 'linear-gradient(45deg, #f093fb, #f5576c)',
    icon: Trophy
  },
  {
    key: 'assists',
    label: '场均助攻',
    value: player.value?.stats.assists || 0,
    change: '+0.8',
    trend: 'up',
    color: 'linear-gradient(45deg, #4facfe, #00f2fe)',
    icon: Share
  },
  {
    key: 'rebounds',
    label: '场均篮板',
    value: player.value?.stats.rebounds || 0,
    change: '-0.5',
    trend: 'down',
    color: 'linear-gradient(45deg, #43e97b, #38f9d7)',
    icon: Basketball
  },
  {
    key: 'efficiency',
    label: '投篮命中率',
    value: player.value ? `${Math.round((player.value.stats.makes / player.value.stats.shots) * 100)}%` : '0%',
    change: '+3.2%',
    trend: 'up',
    color: 'linear-gradient(45deg, #667eea, #764ba2)',
    icon: TrendCharts
  }
])

// 技术统计
const technicalStats = computed(() => [
  {
    label: '投篮命中率',
    value: player.value ? `${player.value.stats.makes}/${player.value.stats.shots}` : '0/0',
    percentage: player.value ? Math.round((player.value.stats.makes / player.value.stats.shots) * 100) : 0,
    color: '#67c23a'
  },
  {
    label: '助攻',
    value: player.value?.stats.assists || 0,
    percentage: Math.min(100, (player.value?.stats.assists || 0) * 10),
    color: '#409eff'
  },
  {
    label: '篮板',
    value: player.value?.stats.rebounds || 0,
    percentage: Math.min(100, (player.value?.stats.rebounds || 0) * 8),
    color: '#e6a23c'
  },
  {
    label: '抢断',
    value: player.value?.stats.steals || 0,
    percentage: Math.min(100, (player.value?.stats.steals || 0) * 20),
    color: '#f56c6c'
  }
])

// 比赛历史
const gameHistory = ref([
  {
    date: '2024-01-15',
    opponent: '金州勇士',
    result: '胜',
    points: 32,
    assists: 9,
    rebounds: 8,
    highlights: 5
  },
  {
    date: '2024-01-12',
    opponent: '波士顿凯尔特人',
    result: '负',
    points: 24,
    assists: 6,
    rebounds: 12,
    highlights: 3
  },
  {
    date: '2024-01-10',
    opponent: '迈阿密热火',
    result: '胜',
    points: 28,
    assists: 7,
    rebounds: 9,
    highlights: 4
  }
])

// 计算属性
const filteredHighlights = computed(() => {
  if (!player.value?.highlights) return []
  
  if (!highlightFilter.value) {
    return player.value.highlights
  }
  
  return player.value.highlights.filter(highlight => {
    // 根据筛选条件过滤
    switch (highlightFilter.value) {
      case 'score':
        return ['shot', 'dunk', 'three_pointer'].includes(highlight.type)
      case 'assist':
        return highlight.type === 'assist'
      case 'defense':
        return ['steal', 'block', 'defense'].includes(highlight.type)
      default:
        return true
    }
  })
})

// 方法
const generateHighlights = () => {
  ElMessage.success('正在生成球员集锦...')
}

const downloadData = () => {
  ElMessage.success('正在下载球员数据...')
}

const sharePlayer = () => {
  const url = window.location.href
  navigator.clipboard.writeText(url)
  ElMessage.success('球员链接已复制到剪贴板!')
}

const playHighlight = (highlight: HighlightClip) => {
  ElMessage.info(`播放精彩片段: ${highlight.description}`)
}

const viewGameDetails = (game: any) => {
  ElMessage.info(`查看比赛详情: vs ${game.opponent}`)
}

const getTypeLabel = (type: HighlightType) => {
  const labels: Record<HighlightType, string> = {
    shot: '投篮',
    dunk: '扣篮',
    three_pointer: '三分',
    assist: '助攻',
    steal: '抢断',
    block: '盖帽',
    rebound: '篮板',
    defense: '防守',
    fast_break: '快攻'
  }
  return labels[type] || type
}

const formatDuration = (seconds: number) => {
  return `${seconds}s`
}

onMounted(() => {
  // 加载球员数据
  player.value = mockPlayer
})
</script>

<style lang="scss" scoped>
.player-page {
  min-height: 100vh;
  padding: 20px;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
}

// 球员头部
.player-header {
  margin-bottom: 30px;
}

.header-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.player-profile {
  display: flex;
  gap: 30px;
  align-items: flex-start;
}

.player-avatar-section {
  position: relative;
  flex-shrink: 0;
}

.player-avatar-large {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border: 4px solid rgba(255, 255, 255, 0.2);
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .el-icon {
    font-size: 48px;
    color: rgba(255, 255, 255, 0.6);
  }
}

.player-status {
  position: absolute;
  bottom: 0;
  right: 0;
}

.player-info {
  flex: 1;
}

.player-name {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 16px;
}

.player-meta {
  display: flex;
  gap: 24px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  font-weight: 600;
}

.value {
  font-size: 16px;
  color: white;
  font-weight: 600;
}

.player-summary {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.player-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex-shrink: 0;
}

// 统计概览
.stats-overview {
  margin-bottom: 30px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: white;
}

.stat-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  margin-bottom: 4px;
}

.stat-change {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  
  &.up {
    color: #67c23a;
  }
  
  &.down {
    color: #f56c6c;
  }
}

// 详细统计
.detailed-stats {
  margin-bottom: 30px;
}

.chart-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  height: 100%;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-weight: 600;
}

.stats-chart {
  padding: 20px 0;
}

.stat-item {
  margin-bottom: 20px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.stat-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.stat-name {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
}

.stat-number {
  color: white;
  font-weight: 600;
}

.trend-chart {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-placeholder {
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  
  .el-icon {
    font-size: 48px;
    margin-bottom: 16px;
  }
}

// 精彩片段
.highlights-section {
  margin-bottom: 30px;
}

.highlights-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-actions {
  margin-left: auto;
}

.highlights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
}

.highlight-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-4px);
  }
}

.highlight-thumbnail {
  position: relative;
  height: 140px;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.thumbnail-placeholder {
  font-size: 32px;
  color: rgba(255, 255, 255, 0.5);
}

.highlight-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.highlight-info {
  padding: 12px;
}

.highlight-title {
  color: white;
  font-weight: 600;
  margin-bottom: 8px;
  font-size: 14px;
}

.highlight-meta {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.no-highlights {
  text-align: center;
  padding: 40px 20px;
}

// 比赛历史
.game-history {
  margin-bottom: 30px;
}

.history-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

// 响应式设计
@media (max-width: 768px) {
  .player-profile {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .player-actions {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .player-meta {
    justify-content: center;
  }
  
  .detailed-stats {
    .el-col {
      margin-bottom: 20px;
    }
  }
}

@media (max-width: 480px) {
  .player-name {
    font-size: 2rem;
  }
  
  .player-actions {
    flex-direction: column;
  }
  
  .highlights-grid {
    grid-template-columns: 1fr;
  }
}
</style>
