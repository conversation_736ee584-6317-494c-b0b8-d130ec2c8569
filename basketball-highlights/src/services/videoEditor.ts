import type { PlayerInfo, HighlightClip } from '../types'

// 视频剪辑配置
interface EditConfig {
  outputFormat: 'mp4' | 'webm'
  quality: 'low' | 'medium' | 'high'
  resolution: '720p' | '1080p' | '4k'
  includeSlowMotion: boolean
  addTransitions: boolean
  includeStats: boolean
}

// 集锦片段信息
interface HighlightSegment {
  startTime: number
  endTime: number
  type: string
  description: string
  confidence: number
}

// 球员集锦数据
interface PlayerHighlight {
  playerId: string
  playerName: string
  segments: HighlightSegment[]
  stats: PlayerStats
  duration: number
  videoUrl?: string
  thumbnailUrl?: string
}

interface PlayerStats {
  totalHighlights: number
  shotsMade: number
  shotsAttempted: number
  assists: number
  steals: number
  blocks: number
  defensiveActions: number
  screenTime: number // 出镜时间（秒）
  averageConfidence: number
}

// 视频编辑服务
export class VideoEditorService {
  private canvas: HTMLCanvasElement
  private ctx: CanvasRenderingContext2D
  private mediaRecorder: MediaRecorder | null = null
  private recordedChunks: Blob[] = []

  constructor() {
    this.canvas = document.createElement('canvas')
    this.ctx = this.canvas.getContext('2d')!
  }

  // 为所有球员生成集锦
  async generateHighlightsForAllPlayers(
    players: PlayerInfo[],
    originalVideoElement: HTMLVideoElement,
    config: EditConfig = this.getDefaultConfig()
  ): Promise<PlayerHighlight[]> {
    const playerHighlights: PlayerHighlight[] = []

    for (const player of players) {
      try {
        console.log(`🎬 正在为球员 ${player.name} 生成集锦...`)
        
        const highlight = await this.generatePlayerHighlight(
          player,
          originalVideoElement,
          config
        )
        
        playerHighlights.push(highlight)
        console.log(`✅ 球员 ${player.name} 集锦生成完成`)
      } catch (error) {
        console.error(`❌ 球员 ${player.name} 集锦生成失败:`, error)
      }
    }

    return playerHighlights
  }

  // 生成单个球员的集锦
  async generatePlayerHighlight(
    player: PlayerInfo,
    originalVideo: HTMLVideoElement,
    config: EditConfig
  ): Promise<PlayerHighlight> {
    // 设置画布尺寸
    this.setupCanvas(config.resolution)
    
    // 准备录制
    const stream = this.canvas.captureStream(30) // 30 FPS
    this.mediaRecorder = new MediaRecorder(stream, {
      mimeType: 'video/webm;codecs=vp9'
    })
    
    this.recordedChunks = []
    
    return new Promise((resolve, reject) => {
      if (!this.mediaRecorder) {
        reject(new Error('MediaRecorder 初始化失败'))
        return
      }

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.recordedChunks.push(event.data)
        }
      }

      this.mediaRecorder.onstop = () => {
        const blob = new Blob(this.recordedChunks, { type: 'video/webm' })
        const videoUrl = URL.createObjectURL(blob)
        
        const highlight: PlayerHighlight = {
          playerId: player.id,
          playerName: player.name || `球员${player.number}`,
          segments: this.convertHighlightsToSegments(player.highlights),
          stats: this.calculatePlayerStats(player),
          duration: this.calculateTotalDuration(player.highlights),
          videoUrl,
          thumbnailUrl: this.generateThumbnail(player.highlights[0])
        }
        
        resolve(highlight)
      }

      this.mediaRecorder.onerror = (error) => {
        reject(error)
      }

      // 开始录制
      this.mediaRecorder.start()
      this.renderPlayerHighlights(player, originalVideo, config)
        .then(() => {
          this.mediaRecorder?.stop()
        })
        .catch(reject)
    })
  }

  // 渲染球员集锦视频
  private async renderPlayerHighlights(
    player: PlayerInfo,
    originalVideo: HTMLVideoElement,
    config: EditConfig
  ): Promise<void> {
    const highlights = player.highlights.sort((a, b) => a.startTime - b.startTime)
    
    for (let i = 0; i < highlights.length; i++) {
      const highlight = highlights[i]
      
      // 添加转场效果
      if (config.addTransitions && i > 0) {
        await this.renderTransition()
      }
      
      // 渲染精彩片段
      await this.renderHighlightClip(highlight, originalVideo, config)
      
      // 添加慢动作效果
      if (config.includeSlowMotion && this.shouldAddSlowMotion(highlight)) {
        await this.renderSlowMotionClip(highlight, originalVideo)
      }
      
      // 添加统计信息覆盖层
      if (config.includeStats) {
        await this.renderStatsOverlay(highlight, player)
      }
    }
    
    // 添加结尾统计总结
    if (config.includeStats) {
      await this.renderFinalStats(player)
    }
  }

  // 渲染单个精彩片段
  private async renderHighlightClip(
    highlight: HighlightClip,
    originalVideo: HTMLVideoElement,
    config: EditConfig
  ): Promise<void> {
    const duration = highlight.endTime - highlight.startTime
    const frameRate = 30
    const totalFrames = Math.floor(duration * frameRate)
    
    for (let frame = 0; frame < totalFrames; frame++) {
      const currentTime = highlight.startTime + (frame / frameRate)
      
      // 设置视频时间
      originalVideo.currentTime = currentTime
      await this.waitForVideoSeek(originalVideo)
      
      // 清除画布
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)
      
      // 绘制视频帧
      this.ctx.drawImage(
        originalVideo,
        0, 0,
        this.canvas.width,
        this.canvas.height
      )
      
      // 添加动作标签
      this.drawActionLabel(highlight)
      
      // 添加球员信息
      this.drawPlayerInfo(highlight)
      
      // 等待下一帧
      await this.waitForNextFrame()
    }
  }

  // 渲染转场效果
  private async renderTransition(): Promise<void> {
    const transitionFrames = 15 // 0.5秒的转场
    
    for (let frame = 0; frame < transitionFrames; frame++) {
      const alpha = frame / transitionFrames
      
      // 渐变效果
      this.ctx.fillStyle = `rgba(0, 0, 0, ${1 - alpha})`
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height)
      
      await this.waitForNextFrame()
    }
  }

  // 渲染慢动作片段
  private async renderSlowMotionClip(
    highlight: HighlightClip,
    originalVideo: HTMLVideoElement
  ): Promise<void> {
    const slowMotionDuration = 2 // 2秒慢动作
    const frameRate = 60 // 高帧率
    const totalFrames = Math.floor(slowMotionDuration * frameRate)
    const clipDuration = highlight.endTime - highlight.startTime
    
    for (let frame = 0; frame < totalFrames; frame++) {
      const progress = frame / totalFrames
      const currentTime = highlight.startTime + (progress * clipDuration)
      
      originalVideo.currentTime = currentTime
      await this.waitForVideoSeek(originalVideo)
      
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)
      this.ctx.drawImage(originalVideo, 0, 0, this.canvas.width, this.canvas.height)
      
      // 添加慢动作标识
      this.drawSlowMotionLabel()
      
      await this.waitForNextFrame()
    }
  }

  // 渲染统计覆盖层
  private async renderStatsOverlay(
    highlight: HighlightClip,
    player: PlayerInfo
  ): Promise<void> {
    const overlayFrames = 60 // 2秒统计显示
    
    for (let frame = 0; frame < overlayFrames; frame++) {
      // 绘制半透明背景
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
      this.ctx.fillRect(0, this.canvas.height - 150, this.canvas.width, 150)
      
      // 绘制统计信息
      this.drawPlayerStats(player, highlight)
      
      await this.waitForNextFrame()
    }
  }

  // 渲染最终统计
  private async renderFinalStats(player: PlayerInfo): Promise<void> {
    const statsFrames = 180 // 6秒统计总结
    
    for (let frame = 0; frame < statsFrames; frame++) {
      // 清除画布
      this.ctx.fillStyle = '#1a1a1a'
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height)
      
      // 绘制球员总结
      this.drawFinalSummary(player)
      
      await this.waitForNextFrame()
    }
  }

  // 绘制动作标签
  private drawActionLabel(highlight: HighlightClip): void {
    const actionNames: Record<string, string> = {
      shot: '投篮',
      dunk: '扣篮',
      assist: '助攻',
      steal: '抢断',
      block: '盖帽',
      defense: '防守'
    }
    
    const label = actionNames[highlight.type] || highlight.type
    
    this.ctx.fillStyle = 'rgba(255, 255, 255, 0.9)'
    this.ctx.fillRect(20, 20, 120, 40)
    
    this.ctx.fillStyle = '#333'
    this.ctx.font = 'bold 16px Arial'
    this.ctx.fillText(label, 30, 45)
  }

  // 绘制球员信息
  private drawPlayerInfo(highlight: HighlightClip): void {
    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
    this.ctx.fillRect(20, this.canvas.height - 80, 200, 60)
    
    this.ctx.fillStyle = 'white'
    this.ctx.font = 'bold 14px Arial'
    this.ctx.fillText(`置信度: ${highlight.confidence.toFixed(1)}%`, 30, this.canvas.height - 50)
    this.ctx.fillText(highlight.description, 30, this.canvas.height - 30)
  }

  // 绘制慢动作标识
  private drawSlowMotionLabel(): void {
    this.ctx.fillStyle = 'rgba(255, 215, 0, 0.8)'
    this.ctx.fillRect(this.canvas.width - 150, 20, 130, 30)
    
    this.ctx.fillStyle = '#333'
    this.ctx.font = 'bold 14px Arial'
    this.ctx.fillText('慢动作回放', this.canvas.width - 140, 40)
  }

  // 绘制球员统计
  private drawPlayerStats(player: PlayerInfo, highlight: HighlightClip): void {
    const stats = player.stats
    const y = this.canvas.height - 120
    
    this.ctx.fillStyle = 'white'
    this.ctx.font = 'bold 16px Arial'
    this.ctx.fillText(`${player.name || '球员' + player.number}`, 20, y + 20)
    
    this.ctx.font = '14px Arial'
    this.ctx.fillText(`得分: ${stats.points}`, 20, y + 45)
    this.ctx.fillText(`助攻: ${stats.assists}`, 120, y + 45)
    this.ctx.fillText(`篮板: ${stats.rebounds}`, 220, y + 45)
    
    this.ctx.fillText(`投篮: ${stats.makes}/${stats.shots}`, 20, y + 70)
    this.ctx.fillText(`命中率: ${((stats.makes / stats.shots) * 100).toFixed(1)}%`, 150, y + 70)
  }

  // 绘制最终总结
  private drawFinalSummary(player: PlayerInfo): void {
    const centerX = this.canvas.width / 2
    const centerY = this.canvas.height / 2
    
    // 标题
    this.ctx.fillStyle = 'white'
    this.ctx.font = 'bold 32px Arial'
    this.ctx.textAlign = 'center'
    this.ctx.fillText(`${player.name || '球员' + player.number} 集锦总结`, centerX, centerY - 100)
    
    // 统计数据
    const stats = player.stats
    this.ctx.font = '24px Arial'
    this.ctx.fillText(`总得分: ${stats.points}`, centerX, centerY - 40)
    this.ctx.fillText(`助攻: ${stats.assists} | 篮板: ${stats.rebounds}`, centerX, centerY)
    this.ctx.fillText(`投篮命中率: ${((stats.makes / stats.shots) * 100).toFixed(1)}%`, centerX, centerY + 40)
    this.ctx.fillText(`精彩片段: ${player.highlights.length} 个`, centerX, centerY + 80)
    
    this.ctx.textAlign = 'left' // 重置对齐方式
  }

  // 工具方法
  private setupCanvas(resolution: string): void {
    const resolutions = {
      '720p': { width: 1280, height: 720 },
      '1080p': { width: 1920, height: 1080 },
      '4k': { width: 3840, height: 2160 }
    }
    
    const { width, height } = resolutions[resolution] || resolutions['1080p']
    this.canvas.width = width
    this.canvas.height = height
  }

  private waitForVideoSeek(video: HTMLVideoElement): Promise<void> {
    return new Promise(resolve => {
      const onSeeked = () => {
        video.removeEventListener('seeked', onSeeked)
        resolve()
      }
      video.addEventListener('seeked', onSeeked)
    })
  }

  private waitForNextFrame(): Promise<void> {
    return new Promise(resolve => {
      requestAnimationFrame(() => resolve())
    })
  }

  private shouldAddSlowMotion(highlight: HighlightClip): boolean {
    return ['dunk', 'shot', 'block'].includes(highlight.type)
  }

  private convertHighlightsToSegments(highlights: HighlightClip[]): HighlightSegment[] {
    return highlights.map(h => ({
      startTime: h.startTime,
      endTime: h.endTime,
      type: h.type,
      description: h.description,
      confidence: h.confidence
    }))
  }

  private calculatePlayerStats(player: PlayerInfo): PlayerStats {
    const highlights = player.highlights
    const stats = player.stats
    
    return {
      totalHighlights: highlights.length,
      shotsMade: stats.makes,
      shotsAttempted: stats.shots,
      assists: stats.assists,
      steals: stats.steals,
      blocks: stats.blocks,
      defensiveActions: highlights.filter(h => h.type === 'defense').length,
      screenTime: highlights.reduce((sum, h) => sum + (h.endTime - h.startTime), 0),
      averageConfidence: highlights.reduce((sum, h) => sum + h.confidence, 0) / highlights.length
    }
  }

  private calculateTotalDuration(highlights: HighlightClip[]): number {
    return highlights.reduce((sum, h) => sum + (h.endTime - h.startTime), 0)
  }

  private generateThumbnail(highlight?: HighlightClip): string {
    if (!highlight) return ''
    
    // 这里可以生成缩略图
    // 暂时返回占位符
    return `data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="120"><rect width="200" height="120" fill="%23667eea"/><text x="100" y="60" text-anchor="middle" fill="white" font-size="14">${highlight.type}</text></svg>`
  }

  private getDefaultConfig(): EditConfig {
    return {
      outputFormat: 'mp4',
      quality: 'medium',
      resolution: '1080p',
      includeSlowMotion: true,
      addTransitions: true,
      includeStats: true
    }
  }

  // 清理资源
  dispose(): void {
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop()
    }
    this.recordedChunks = []
  }
}

// 导出单例实例
export const videoEditorService = new VideoEditorService()

// 导出生成函数供外部调用
export async function generateHighlightsForAllPlayers(
  analysisResults: { players: PlayerInfo[] }
): Promise<PlayerHighlight[]> {
  // 这里需要获取原始视频元素
  // 在实际应用中，这个视频元素应该从分析页面传递过来
  const videoElement = document.createElement('video')
  // videoElement.src = originalVideoUrl
  
  return videoEditorService.generateHighlightsForAllPlayers(
    analysisResults.players,
    videoElement
  )
}
