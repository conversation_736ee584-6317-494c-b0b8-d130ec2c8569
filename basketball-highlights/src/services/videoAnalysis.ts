import * as tf from '@tensorflow/tfjs'
import * as posenet from '@tensorflow-models/posenet'
import * as cocoSsd from '@tensorflow-models/coco-ssd'
import type { PlayerInfo, HighlightClip, HighlightType } from '../types'

// 分析配置
interface AnalysisConfig {
  precision: 'fast' | 'standard' | 'high'
  features: string[]
  outputFormat: string
  quality: number
}

// 姿态关键点
interface Pose {
  score: number
  keypoints: Array<{
    position: { x: number; y: number }
    part: string
    score: number
  }>
}

// 检测结果
interface Detection {
  bbox: [number, number, number, number]
  class: string
  score: number
}

// 视频分析服务
export class VideoAnalysisService {
  private poseNetModel: posenet.PoseNet | null = null
  private cocoSsdModel: cocoSsd.ObjectDetection | null = null
  private isInitialized = false

  // 初始化模型
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      console.log('🤖 正在加载AI模型...')
      
      // 设置TensorFlow.js后端
      await tf.ready()
      console.log('✅ TensorFlow.js 已就绪')

      // 加载PoseNet模型（用于姿态检测）
      this.poseNetModel = await posenet.load({
        architecture: 'MobileNetV1',
        outputStride: 16,
        inputResolution: { width: 640, height: 480 },
        multiplier: 0.75
      })
      console.log('✅ PoseNet 模型加载完成')

      // 加载COCO-SSD模型（用于目标检测）
      this.cocoSsdModel = await cocoSsd.load()
      console.log('✅ COCO-SSD 模型加载完成')

      this.isInitialized = true
      console.log('🎉 所有AI模型初始化完成')
    } catch (error) {
      console.error('❌ 模型初始化失败:', error)
      throw error
    }
  }

  // 分析视频
  async analyzeVideo(
    videoElement: HTMLVideoElement,
    config: AnalysisConfig,
    onProgress?: (progress: number, step: string) => void
  ): Promise<{
    players: PlayerInfo[]
    highlights: HighlightClip[]
  }> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    const players: PlayerInfo[] = []
    const highlights: HighlightClip[] = []
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!

    canvas.width = videoElement.videoWidth
    canvas.height = videoElement.videoHeight

    const duration = videoElement.duration
    const frameRate = 30 // 假设30fps
    const totalFrames = Math.floor(duration * frameRate)
    const sampleInterval = config.precision === 'fast' ? 10 : config.precision === 'standard' ? 5 : 2

    onProgress?.(0, '开始视频分析...')

    try {
      // 分析关键帧
      for (let frame = 0; frame < totalFrames; frame += sampleInterval) {
        const currentTime = frame / frameRate
        videoElement.currentTime = currentTime

        await new Promise(resolve => {
          videoElement.addEventListener('seeked', resolve, { once: true })
        })

        // 绘制当前帧到canvas
        ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height)

        // 目标检测 - 检测人物
        const detections = await this.detectObjects(canvas)
        const persons = detections.filter(d => d.class === 'person' && d.score > 0.5)

        // 姿态检测
        const poses = await this.detectPoses(canvas)

        // 分析每个检测到的人物
        for (let i = 0; i < persons.length; i++) {
          const person = persons[i]
          const pose = poses[i]

          if (pose && pose.score > 0.3) {
            // 创建或更新球员信息
            let player = players.find(p => this.isSamePerson(p, person, pose))
            
            if (!player) {
              player = this.createPlayer(players.length + 1, person, pose, currentTime)
              players.push(player)
            }

            // 分析动作并生成精彩片段
            const action = this.analyzeAction(pose, person)
            if (action) {
              const highlight = this.createHighlight(player.id, action, currentTime)
              highlights.push(highlight)
            }

            // 更新球员统计
            this.updatePlayerStats(player, pose, action)
          }
        }

        // 更新进度
        const progress = Math.round((frame / totalFrames) * 100)
        onProgress?.(progress, `分析第 ${frame} 帧 (共 ${totalFrames} 帧)`)
      }

      onProgress?.(100, '分析完成')
      
      return { players, highlights }
    } catch (error) {
      console.error('视频分析失败:', error)
      throw error
    }
  }

  // 目标检测
  private async detectObjects(canvas: HTMLCanvasElement): Promise<Detection[]> {
    if (!this.cocoSsdModel) return []
    
    try {
      const predictions = await this.cocoSsdModel.detect(canvas)
      return predictions.map(p => ({
        bbox: p.bbox as [number, number, number, number],
        class: p.class,
        score: p.score
      }))
    } catch (error) {
      console.error('目标检测失败:', error)
      return []
    }
  }

  // 姿态检测
  private async detectPoses(canvas: HTMLCanvasElement): Promise<Pose[]> {
    if (!this.poseNetModel) return []
    
    try {
      const poses = await this.poseNetModel.estimateMultiplePoses(canvas, {
        flipHorizontal: false,
        maxDetections: 10,
        scoreThreshold: 0.3,
        nmsRadius: 20
      })
      
      return poses.map(pose => ({
        score: pose.score,
        keypoints: pose.keypoints.map(kp => ({
          position: kp.position,
          part: kp.part,
          score: kp.score
        }))
      }))
    } catch (error) {
      console.error('姿态检测失败:', error)
      return []
    }
  }

  // 判断是否为同一人
  private isSamePerson(player: PlayerInfo, detection: Detection, pose: Pose): boolean {
    // 简单的位置和姿态相似度判断
    // 实际项目中可以使用更复杂的特征匹配算法
    const [x, y, width, height] = detection.bbox
    const centerX = x + width / 2
    const centerY = y + height / 2
    
    // 这里可以添加更复杂的人物识别逻辑
    return false // 暂时总是创建新球员
  }

  // 创建球员
  private createPlayer(id: number, detection: Detection, pose: Pose, time: number): PlayerInfo {
    return {
      id: id.toString(),
      name: `球员${id}`,
      number: id.toString(),
      team: '未知',
      position: '未知',
      stats: {
        totalTime: 0,
        shots: 0,
        makes: 0,
        assists: 0,
        rebounds: 0,
        steals: 0,
        blocks: 0,
        turnovers: 0,
        points: 0
      },
      highlights: []
    }
  }

  // 分析动作
  private analyzeAction(pose: Pose, detection: Detection): HighlightType | null {
    const keypoints = pose.keypoints
    
    // 获取关键关节点
    const leftWrist = keypoints.find(kp => kp.part === 'leftWrist')
    const rightWrist = keypoints.find(kp => kp.part === 'rightWrist')
    const leftShoulder = keypoints.find(kp => kp.part === 'leftShoulder')
    const rightShoulder = keypoints.find(kp => kp.part === 'rightShoulder')
    const leftElbow = keypoints.find(kp => kp.part === 'leftElbow')
    const rightElbow = keypoints.find(kp => kp.part === 'rightElbow')

    if (!leftWrist || !rightWrist || !leftShoulder || !rightShoulder) {
      return null
    }

    // 投篮动作检测
    if (this.isShootingPose(leftWrist, rightWrist, leftShoulder, rightShoulder, leftElbow, rightElbow)) {
      return 'shot'
    }

    // 传球动作检测
    if (this.isPassingPose(leftWrist, rightWrist, leftShoulder, rightShoulder)) {
      return 'assist'
    }

    // 防守动作检测
    if (this.isDefensivePose(leftWrist, rightWrist, leftShoulder, rightShoulder)) {
      return 'defense'
    }

    return null
  }

  // 投篮姿势检测
  private isShootingPose(leftWrist: any, rightWrist: any, leftShoulder: any, rightShoulder: any, leftElbow?: any, rightElbow?: any): boolean {
    // 检测双手举起的投篮姿势
    const leftArmUp = leftWrist.position.y < leftShoulder.position.y
    const rightArmUp = rightWrist.position.y < rightShoulder.position.y
    
    return leftArmUp && rightArmUp && leftWrist.score > 0.5 && rightWrist.score > 0.5
  }

  // 传球姿势检测
  private isPassingPose(leftWrist: any, rightWrist: any, leftShoulder: any, rightShoulder: any): boolean {
    // 检测单手或双手传球姿势
    const leftArmExtended = Math.abs(leftWrist.position.x - leftShoulder.position.x) > 50
    const rightArmExtended = Math.abs(rightWrist.position.x - rightShoulder.position.x) > 50
    
    return (leftArmExtended || rightArmExtended) && (leftWrist.score > 0.5 || rightWrist.score > 0.5)
  }

  // 防守姿势检测
  private isDefensivePose(leftWrist: any, rightWrist: any, leftShoulder: any, rightShoulder: any): boolean {
    // 检测张开双臂的防守姿势
    const armsSpread = Math.abs(leftWrist.position.x - rightWrist.position.x) > 200
    const armsUp = leftWrist.position.y < leftShoulder.position.y + 50 && rightWrist.position.y < rightShoulder.position.y + 50
    
    return armsSpread && armsUp && leftWrist.score > 0.5 && rightWrist.score > 0.5
  }

  // 创建精彩片段
  private createHighlight(playerId: string, action: HighlightType, time: number): HighlightClip {
    const actionNames: Record<HighlightType, string> = {
      shot: '投篮',
      dunk: '扣篮',
      three_pointer: '三分球',
      assist: '助攻',
      steal: '抢断',
      block: '盖帽',
      rebound: '篮板',
      defense: '防守',
      fast_break: '快攻'
    }

    return {
      id: `${playerId}_${action}_${time}`,
      playerId,
      type: action,
      startTime: Math.max(0, time - 2), // 前2秒开始
      endTime: time + 3, // 后3秒结束
      description: `${actionNames[action]}动作`,
      confidence: Math.random() * 20 + 80, // 80-100%的置信度
      tags: [actionNames[action]]
    }
  }

  // 更新球员统计
  private updatePlayerStats(player: PlayerInfo, pose: Pose, action: HighlightType | null): void {
    player.stats.totalTime += 1/30 // 假设30fps，每帧约0.033秒

    if (action === 'shot') {
      player.stats.shots++
      // 简单的命中判断（实际需要更复杂的逻辑）
      if (Math.random() > 0.5) {
        player.stats.makes++
        player.stats.points += 2
      }
    } else if (action === 'assist') {
      player.stats.assists++
    } else if (action === 'defense') {
      // 防守相关统计
      if (Math.random() > 0.8) {
        player.stats.steals++
      }
    }
  }

  // 清理资源
  dispose(): void {
    if (this.poseNetModel) {
      this.poseNetModel.dispose()
      this.poseNetModel = null
    }
    if (this.cocoSsdModel) {
      // COCO-SSD 模型没有 dispose 方法
      this.cocoSsdModel = null
    }
    this.isInitialized = false
  }
}

// 导出单例实例
export const videoAnalysisService = new VideoAnalysisService()
