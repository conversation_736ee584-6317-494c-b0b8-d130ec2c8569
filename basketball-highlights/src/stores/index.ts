import { defineStore } from 'pinia'
import type { VideoInfo, PlayerInfo, HighlightClip } from '@/types'

// 主应用状态
export const useAppStore = defineStore('app', {
  state: () => ({
    loading: false,
    currentVideo: null as VideoInfo | null,
    players: [] as PlayerInfo[],
    highlights: [] as HighlightClip[]
  }),

  getters: {
    isVideoLoaded: (state) => !!state.currentVideo,
    playerCount: (state) => state.players.length,
    highlightCount: (state) => state.highlights.length
  },

  actions: {
    setLoading(loading: boolean) {
      this.loading = loading
    },

    setCurrentVideo(video: VideoInfo) {
      this.currentVideo = video
    },

    setPlayers(players: PlayerInfo[]) {
      this.players = players
    },

    addPlayer(player: PlayerInfo) {
      this.players.push(player)
    },

    updatePlayer(id: string, updates: Partial<PlayerInfo>) {
      const index = this.players.findIndex(p => p.id === id)
      if (index !== -1) {
        this.players[index] = { ...this.players[index], ...updates }
      }
    },

    setHighlights(highlights: HighlightClip[]) {
      this.highlights = highlights
    },

    addHighlight(highlight: HighlightClip) {
      this.highlights.push(highlight)
    },

    removeHighlight(id: string) {
      const index = this.highlights.findIndex(h => h.id === id)
      if (index !== -1) {
        this.highlights.splice(index, 1)
      }
    },

    clearData() {
      this.currentVideo = null
      this.players = []
      this.highlights = []
    }
  }
})

// 视频分析状态
export const useAnalysisStore = defineStore('analysis', {
  state: () => ({
    analysisProgress: 0,
    currentStep: '',
    isAnalyzing: false,
    analysisResults: null as any
  }),

  actions: {
    startAnalysis() {
      this.isAnalyzing = true
      this.analysisProgress = 0
      this.currentStep = '开始分析...'
    },

    updateProgress(progress: number, step: string) {
      this.analysisProgress = progress
      this.currentStep = step
    },

    completeAnalysis(results: any) {
      this.isAnalyzing = false
      this.analysisProgress = 100
      this.currentStep = '分析完成'
      this.analysisResults = results
    },

    resetAnalysis() {
      this.analysisProgress = 0
      this.currentStep = ''
      this.isAnalyzing = false
      this.analysisResults = null
    }
  }
})
