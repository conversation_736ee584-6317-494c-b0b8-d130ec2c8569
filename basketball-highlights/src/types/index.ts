// 视频信息
export interface VideoInfo {
  id: string
  name: string
  url: string
  duration: number
  size: number
  uploadTime: string
  thumbnail?: string
  resolution?: {
    width: number
    height: number
  }
}

// 球员信息
export interface PlayerInfo {
  id: string
  name: string
  number?: string
  team?: string
  position?: string
  avatar?: string
  stats: PlayerStats
  highlights: HighlightClip[]
}

// 球员统计
export interface PlayerStats {
  totalTime: number // 出场时间（秒）
  shots: number // 投篮次数
  makes: number // 命中次数
  assists: number // 助攻
  rebounds: number // 篮板
  steals: number // 抢断
  blocks: number // 盖帽
  turnovers: number // 失误
  points: number // 得分
}

// 精彩片段
export interface HighlightClip {
  id: string
  playerId: string
  type: HighlightType
  startTime: number
  endTime: number
  description: string
  confidence: number // AI识别置信度
  thumbnail?: string
  tags: string[]
}

// 精彩片段类型
export enum HighlightType {
  SHOT = 'shot', // 投篮
  DUNK = 'dunk', // 扣篮
  THREE_POINTER = 'three_pointer', // 三分球
  ASSIST = 'assist', // 助攻
  STEAL = 'steal', // 抢断
  BLOCK = 'block', // 盖帽
  REBOUND = 'rebound', // 篮板
  DEFENSE = 'defense', // 防守
  FAST_BREAK = 'fast_break' // 快攻
}

// 分析任务
export interface AnalysisTask {
  id: string
  videoId: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  startTime: string
  endTime?: string
  results?: AnalysisResults
}

// 分析结果
export interface AnalysisResults {
  players: PlayerInfo[]
  highlights: HighlightClip[]
  gameStats: GameStats
  timeline: TimelineEvent[]
}

// 比赛统计
export interface GameStats {
  duration: number
  totalPlayers: number
  totalHighlights: number
  averageConfidence: number
  teamStats: {
    [teamName: string]: {
      players: number
      points: number
      highlights: number
    }
  }
}

// 时间轴事件
export interface TimelineEvent {
  id: string
  time: number
  type: string
  description: string
  playerId?: string
  confidence: number
}

// API响应
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 上传进度
export interface UploadProgress {
  loaded: number
  total: number
  percentage: number
}
