import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/Home.vue'),
    meta: {
      title: '首页'
    }
  },
  {
    path: '/upload',
    name: 'Upload',
    component: () => import('@/views/Upload.vue'),
    meta: {
      title: '视频上传'
    }
  },
  {
    path: '/analysis/:id',
    name: 'Analysis',
    component: () => import('@/views/Analysis.vue'),
    meta: {
      title: '视频分析'
    }
  },
  {
    path: '/highlights/:id',
    name: 'Highlights',
    component: () => import('@/views/Highlights.vue'),
    meta: {
      title: '个人集锦'
    }
  },
  {
    path: '/player/:id',
    name: 'Player',
    component: () => import('@/views/Player.vue'),
    meta: {
      title: '球员详情'
    }
  },
  {
    path: '/gallery',
    name: 'Gallery',
    component: () => import('@/views/Gallery.vue'),
    meta: {
      title: '集锦画廊'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: {
      title: '页面未找到'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  document.title = `${to.meta.title} - 篮球视频集锦生成器`
  next()
})

export default router
