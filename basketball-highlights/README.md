# 🏀 篮球视频个人集锦生成器

一个基于Vue3的AI驱动篮球视频分析和个人集锦生成平台。通过先进的计算机视觉技术，自动识别篮球比赛中的球员动作，生成个性化的精彩集锦视频。

## ✨ 功能特色

### 🎯 核心功能
- **AI视频分析**: 使用先进的计算机视觉技术自动分析篮球视频
- **球员识别**: 智能识别和追踪每位球员的表现
- **动作检测**: 自动识别投篮、扣篮、助攻、防守等关键动作
- **集锦生成**: 根据分析结果自动剪辑生成个人精彩集锦
- **数据统计**: 详细的个人和团队数据分析

### 🎨 用户体验
- **现代化UI**: 采用渐变背景和毛玻璃效果的现代设计
- **响应式布局**: 完美适配桌面、平板和移动设备
- **实时反馈**: 分析进度实时显示，用户体验流畅
- **交互动画**: 丰富的动画效果和交互反馈

### 📊 数据可视化
- **统计图表**: 球员数据的可视化展示
- **表现趋势**: 历史表现数据分析
- **对比分析**: 多球员数据对比功能

## 🛠️ 技术栈

### 前端框架
- **Vue 3**: 采用Composition API的现代Vue框架
- **TypeScript**: 类型安全的JavaScript超集
- **Vite**: 快速的构建工具和开发服务器

### UI组件库
- **Element Plus**: 基于Vue3的组件库
- **Element Plus Icons**: 丰富的图标库

### 状态管理
- **Pinia**: Vue3官方推荐的状态管理库

### 路由
- **Vue Router 4**: Vue3的官方路由管理器

### 样式
- **SCSS**: CSS预处理器
- **响应式设计**: 移动端优先的设计理念

### 视频处理
- **Video.js**: 强大的HTML5视频播放器
- **Canvas API**: 用于视频帧处理和分析

### 数据可视化
- **Chart.js**: 灵活的图表库
- **Vue-ChartJS**: Chart.js的Vue3封装

## 📁 项目结构

```
basketball-highlights/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 可复用组件
│   │   └── layout/        # 布局组件
│   ├── views/             # 页面组件
│   ├── stores/            # Pinia状态管理
│   ├── router/            # 路由配置
│   ├── types/             # TypeScript类型定义
│   ├── styles/            # 全局样式
│   ├── utils/             # 工具函数
│   ├── App.vue            # 根组件
│   └── main.ts            # 应用入口
├── package.json           # 项目配置
├── vite.config.ts         # Vite配置
├── tsconfig.json          # TypeScript配置
└── README.md              # 项目说明
```

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 7.0.0 或 yarn >= 1.22.0

### 安装依赖
```bash
# 使用npm
npm install

# 或使用yarn
yarn install

# 或使用pnpm (推荐)
pnpm install
```

### 开发环境
```bash
# 启动开发服务器
npm run dev

# 或
yarn dev

# 或
pnpm dev
```

访问 http://localhost:3000 查看应用

### 构建生产版本
```bash
# 构建生产版本
npm run build

# 或
yarn build

# 或
pnpm build
```

### 预览生产版本
```bash
# 预览构建结果
npm run preview

# 或
yarn preview

# 或
pnpm preview
```

## 📱 页面功能

### 🏠 首页 (/)
- 产品介绍和功能展示
- 工作流程说明
- 统计数据展示
- 快速导航

### 📤 视频上传 (/upload)
- 拖拽上传视频文件
- 上传进度显示
- 分析选项配置
- 历史上传记录

### 📊 视频分析 (/analysis/:id)
- 实时分析进度
- 分析步骤展示
- 预览结果显示
- 取消/重新分析

### ⭐ 个人集锦 (/highlights/:id)
- 球员选择界面
- 集锦视频播放
- 精彩片段管理
- 数据统计展示

### 👤 球员详情 (/player/:id)
- 球员基本信息
- 详细统计数据
- 表现趋势图表
- 比赛历史记录

### 🖼️ 集锦画廊 (/gallery)
- 所有集锦视频展示
- 搜索和筛选功能
- 视频播放和分享
- 点赞和下载功能

## 🎨 设计特色

### 视觉设计
- **渐变背景**: 医疗主题的蓝紫色渐变
- **毛玻璃效果**: backdrop-filter实现的现代感
- **卡片式布局**: 清晰的信息层次结构
- **动画效果**: 流畅的交互动画

### 交互设计
- **悬停效果**: 卡片阴影和位移动画
- **点击反馈**: Material Design风格的波纹效果
- **加载状态**: 优雅的加载动画和进度指示
- **错误处理**: 友好的错误提示和恢复建议

## 🔧 配置说明

### Vite配置
- 自动导入Vue和Element Plus组件
- 路径别名配置 (@指向src目录)
- 开发服务器端口3000
- 生产构建优化

### TypeScript配置
- 严格模式启用
- 路径映射配置
- 类型检查优化

### 样式配置
- SCSS全局变量
- 响应式断点定义
- 工具类生成
- 深色主题支持

## 🌟 未来规划

### 功能扩展
- [ ] 实时视频流分析
- [ ] 多语言支持
- [ ] 社交分享功能
- [ ] 云端存储集成
- [ ] 移动端APP

### 技术优化
- [ ] PWA支持
- [ ] 服务端渲染(SSR)
- [ ] 性能监控
- [ ] 自动化测试
- [ ] CI/CD流水线

### AI能力
- [ ] 更精确的球员识别
- [ ] 战术分析功能
- [ ] 预测分析
- [ ] 自然语言查询
- [ ] 智能推荐系统

## 🤝 贡献指南

欢迎提交Issue和Pull Request来帮助改进项目！

### 开发规范
- 使用TypeScript编写代码
- 遵循Vue3 Composition API最佳实践
- 保持代码风格一致
- 添加适当的注释和文档

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构
- test: 测试相关
- chore: 构建过程或辅助工具的变动

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 📞 联系我们

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 功能建议: [Discussions]

---

**让AI为篮球带来更多精彩！** 🏀✨
