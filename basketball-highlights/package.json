{"name": "basketball-highlights", "version": "1.0.0", "description": "篮球视频个人集锦生成网站", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@tensorflow-models/coco-ssd": "^2.2.3", "@tensorflow-models/posenet": "^2.2.2", "@tensorflow/tfjs": "^4.22.0", "axios": "^1.6.2", "chart.js": "^4.4.1", "dayjs": "^1.11.10", "element-plus": "^2.4.4", "fabric": "^5.3.0", "pinia": "^2.1.7", "video.js": "^8.6.1", "vue": "^3.4.0", "vue-chartjs": "^5.3.0", "vue-router": "^4.2.5"}, "devDependencies": {"@types/node": "^20.10.5", "@vitejs/plugin-vue": "^4.5.2", "sass": "^1.69.5", "typescript": "^5.2.2", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.8", "vue-tsc": "^1.8.25"}}