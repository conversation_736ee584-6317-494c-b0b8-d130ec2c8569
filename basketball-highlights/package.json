{"name": "basketball-highlights", "version": "1.0.0", "description": "篮球视频个人集锦生成网站", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "video.js": "^8.6.1", "fabric": "^5.3.0", "chart.js": "^4.4.1", "vue-chartjs": "^5.3.0", "dayjs": "^1.11.10"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.8", "typescript": "^5.2.2", "vue-tsc": "^1.8.25", "@types/node": "^20.10.5", "sass": "^1.69.5", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0"}}