<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能审方系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        /* 顶部导航栏 */
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 12px 24px;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 20px;
            font-weight: 600;
            color: #2563eb;
        }

        .logo-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(45deg, #2563eb, #3b82f6);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
            color: #64748b;
        }

        .nav-item:hover {
            background: #f1f5f9;
            color: #2563eb;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 16px;
            background: #f8fafc;
            border-radius: 12px;
        }

        .avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(45deg, #10b981, #34d399);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        /* 主要内容区域 */
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 24px;
        }

        /* 欢迎区域 */
        .welcome-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 32px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .ai-avatar {
            width: 80px;
            height: 80px;
            margin: 0 auto 24px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .welcome-title {
            font-size: 28px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 12px;
        }

        .welcome-subtitle {
            font-size: 16px;
            color: #64748b;
            margin-bottom: 32px;
        }

        .primary-button {
            background: linear-gradient(45deg, #2563eb, #3b82f6);
            color: white;
            border: none;
            padding: 16px 32px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(37, 99, 235, 0.3);
        }

        .primary-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(37, 99, 235, 0.4);
        }

        /* 功能卡片区域 */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 32px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 20px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .feature-icon.primary { background: linear-gradient(45deg, #2563eb, #3b82f6); }
        .feature-icon.success { background: linear-gradient(45deg, #10b981, #34d399); }
        .feature-icon.warning { background: linear-gradient(45deg, #f59e0b, #fbbf24); }
        .feature-icon.info { background: linear-gradient(45deg, #8b5cf6, #a78bfa); }

        .feature-title {
            font-size: 18px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 12px;
        }

        .feature-description {
            font-size: 14px;
            color: #64748b;
            line-height: 1.6;
        }

        /* 统计数据区域 */
        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            padding: 24px;
            text-align: center;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
        }

        .stat-number.primary { color: #2563eb; }
        .stat-number.success { color: #10b981; }
        .stat-number.warning { color: #f59e0b; }

        .stat-label {
            font-size: 14px;
            color: #64748b;
            font-weight: 500;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .navbar {
                padding: 12px 16px;
            }
            
            .main-container {
                padding: 24px 16px;
            }
            
            .welcome-section {
                padding: 24px;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-right {
                gap: 12px;
            }
            
            .nav-item span {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar">
        <div class="logo">
            <div class="logo-icon">🏥</div>
            <span>AI智能审方系统</span>
        </div>
        <div class="nav-right">
            <div class="nav-item">
                <span>📊</span>
                <span>数据中心</span>
            </div>
            <div class="nav-item">
                <span>🔔</span>
                <span>消息通知</span>
            </div>
            <div class="nav-item">
                <span>⚙️</span>
                <span>系统设置</span>
            </div>
            <div class="user-info">
                <div class="avatar">李</div>
                <div>
                    <div style="font-weight: 600; font-size: 14px;">李医师</div>
                    <div style="font-size: 12px; color: #10b981;">● 在线</div>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="main-container">
        <!-- 欢迎区域 -->
        <section class="welcome-section">
            <div class="ai-avatar">🤖</div>
            <h1 class="welcome-title">您好，李医师！欢迎使用AI智能审方系统</h1>
            <p class="welcome-subtitle">我是您的AI审方助手，为您提供专业、高效的处方审核服务</p>
            <button class="primary-button" onclick="startAudit()">🚀 开始智能审方</button>
        </section>

        <!-- 功能卡片区域 -->
        <section class="features-grid">
            <div class="feature-card" onclick="quickAudit()">
                <div class="feature-icon primary">⚡</div>
                <h3 class="feature-title">一键审方</h3>
                <p class="feature-description">快速上传处方，AI智能分析，秒级出具审核结果</p>
            </div>
            
            <div class="feature-card" onclick="viewHistory()">
                <div class="feature-icon success">📋</div>
                <h3 class="feature-title">历史记录</h3>
                <p class="feature-description">查看历史审方记录，追踪处方状态和审核详情</p>
            </div>
            
            <div class="feature-card" onclick="viewAnalytics()">
                <div class="feature-icon warning">📈</div>
                <h3 class="feature-title">数据分析</h3>
                <p class="feature-description">深度分析审方数据，提供专业的统计报告</p>
            </div>
            
            <div class="feature-card" onclick="openSettings()">
                <div class="feature-icon info">🔧</div>
                <h3 class="feature-title">系统设置</h3>
                <p class="feature-description">个性化配置审方规则，优化工作流程</p>
            </div>
        </section>

        <!-- 统计数据区域 -->
        <section class="stats-section">
            <div class="stat-card">
                <div class="stat-number primary">156</div>
                <div class="stat-label">今日审方数量</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number success">98.5%</div>
                <div class="stat-label">审方通过率</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number warning">3</div>
                <div class="stat-label">异常处方</div>
            </div>
            
            <div class="stat-card">
                <div class="stat-number primary">2.3s</div>
                <div class="stat-label">平均审方时间</div>
            </div>
        </section>
    </div>

    <script>
        // 交互功能
        function startAudit() {
            alert('正在启动AI智能审方系统...');
        }
        
        function quickAudit() {
            alert('打开一键审方功能');
        }
        
        function viewHistory() {
            alert('查看历史记录');
        }
        
        function viewAnalytics() {
            alert('打开数据分析');
        }
        
        function openSettings() {
            alert('打开系统设置');
        }

        // 添加一些动态效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为卡片添加点击波纹效果
            const cards = document.querySelectorAll('.feature-card, .stat-card');
            cards.forEach(card => {
                card.addEventListener('click', function(e) {
                    const ripple = document.createElement('div');
                    ripple.style.position = 'absolute';
                    ripple.style.borderRadius = '50%';
                    ripple.style.background = 'rgba(37, 99, 235, 0.3)';
                    ripple.style.transform = 'scale(0)';
                    ripple.style.animation = 'ripple 0.6s linear';
                    ripple.style.left = (e.clientX - card.offsetLeft - 25) + 'px';
                    ripple.style.top = (e.clientY - card.offsetTop - 25) + 'px';
                    ripple.style.width = ripple.style.height = '50px';
                    
                    card.style.position = 'relative';
                    card.appendChild(ripple);
                    
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        });
    </script>

    <style>
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    </style>
</body>
</html>
